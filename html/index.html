<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UCard 用户中心</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #1e293b;
            margin: 0;
            padding: 0;
        }

        .container {
            display: flex;
            min-height: 100vh;
            background: #f8fafc;
        }

        /* 左侧菜单 */
        .sidebar {
            width: 280px;
            background: #ffffff;
            border-right: 1px solid #e2e8f0;
            padding: 0;
            position: relative;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .logo {
            padding: 32px 24px;
            border-bottom: 1px solid #e2e8f0;
            margin-bottom: 8px;
        }

        .logo h1 {
            font-size: 28px;
            font-weight: 700;
            color: #f59e0b;
            margin: 0;
            letter-spacing: -0.5px;
        }

        .logo p {
            font-size: 13px;
            color: #64748b;
            margin: 4px 0 0 0;
            font-weight: 400;
        }

        .menu {
            list-style: none;
            padding: 8px 0;
        }

        .menu-item {
            margin: 2px 0;
        }

        .menu-item a {
            display: flex;
            align-items: center;
            padding: 14px 24px;
            color: #64748b;
            text-decoration: none;
            transition: all 0.2s ease;
            border-radius: 0;
            font-weight: 500;
            font-size: 14px;
            cursor: pointer;
        }

        .menu-item a:hover {
            background: #f1f5f9;
            color: #1e293b;
        }

        .menu-item a.active {
            background: #fef3c7;
            color: #f59e0b;
            border-right: 2px solid #f59e0b;
        }

        .menu-item .icon {
            margin-right: 12px;
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        /* 右侧内容区域 */
        .main-content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            background: #f8fafc;
        }

        .content-header {
            background: #ffffff;
            padding: 32px;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            margin-bottom: 24px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .content-header h2 {
            font-size: 32px;
            color: #1e293b;
            margin-bottom: 8px;
            font-weight: 600;
            letter-spacing: -0.5px;
        }

        .content-header p {
            color: #64748b;
            font-size: 16px;
            margin: 0;
            font-weight: 400;
        }

        .content-body {
            background: #ffffff;
            padding: 32px;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            min-height: 500px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        /* 快捷操作卡片 */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 32px;
        }

        .action-card {
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            color: inherit;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .action-card:hover {
            background: #fefbf3;
            border-color: #f59e0b;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
        }

        .action-icon {
            font-size: 32px;
            margin-bottom: 12px;
            display: block;
        }

        .action-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .action-desc {
            font-size: 14px;
            color: #64748b;
        }

        /* 卡片样式 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 24px;
        }

        .card-item {
            background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
            border-radius: 16px;
            padding: 28px;
            color: white;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            box-shadow: 0 4px 12px 0 rgba(245, 158, 11, 0.25);
        }

        .card-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 24px 0 rgba(245, 158, 11, 0.4);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-label {
            font-size: 16px;
            font-weight: 600;
        }

        .card-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-normal {
            background: rgba(46, 204, 113, 0.2);
            color: #2ecc71;
        }

        .status-frozen {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
        }

        .card-number {
            font-size: 18px;
            font-weight: 600;
            letter-spacing: 2px;
            margin-bottom: 15px;
            font-family: 'Courier New', monospace;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .eye-icon {
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: background-color 0.2s ease;
            opacity: 0.7;
        }

        .eye-icon:hover {
            background-color: #f3f4f6;
            opacity: 1;
        }

        .card-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .card-info-item {
            text-align: center;
        }

        .card-info-label {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .card-info-value {
            font-size: 14px;
            font-weight: 600;
        }

        .card-balance {
            font-size: 24px;
            font-weight: 700;
            text-align: center;
            margin-bottom: 20px;
        }

        .card-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 70px;
        }

        .btn-primary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-primary:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .btn-danger {
            background: rgba(231, 76, 60, 0.2);
            color: #e74c3c;
            border: 1px solid rgba(231, 76, 60, 0.3);
        }

        .btn-success {
            background: rgba(46, 204, 113, 0.2);
            color: #2ecc71;
            border: 1px solid rgba(46, 204, 113, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧菜单 -->
        <nav class="sidebar">
            <div class="logo">
                <h1>UCard</h1>
                <p>数字货币虚拟信用卡</p>
            </div>
            
            <ul class="menu">
                <li class="menu-item">
                    <a href="#" onclick="loadPage('card-management')" class="active">
                        <span class="icon">💳</span>
                        <span>卡片管理</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" onclick="loadPage('card-application')">
                        <span class="icon">📝</span>
                        <span>开卡申请</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" onclick="clearSelectedCard(); loadPage('card-recharge')">
                        <span class="icon">💰</span>
                        <span>卡片充值</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" onclick="loadPage('transaction-query')">
                        <span class="icon">📊</span>
                        <span>交易查询</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" onclick="loadPage('balance-query')">
                        <span class="icon">💵</span>
                        <span>余额查询</span>
                    </a>
                </li>
                <li class="menu-item">
                    <a href="#" onclick="loadPage('notification-center')">
                        <span class="icon">🔔</span>
                        <span>通知中心</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- 右侧内容区域 -->
        <main class="main-content">
            <div class="content-header">
                <h2 id="page-title">卡片管理</h2>
                <p id="page-description">管理您的所有UCard虚拟信用卡</p>
            </div>
            
            <div class="content-body" id="content-body">
                <!-- 默认显示卡片管理内容 -->


                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3 style="margin: 0; color: #1e293b; font-size: 18px; font-weight: 600;">我的卡片</h3>
                    <button onclick="loadPage('card-application')" style="
                        background: #f59e0b;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 8px;
                        font-size: 14px;
                        font-weight: 500;
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        gap: 6px;
                    ">
                        <span>📝</span>
                        申请新卡
                    </button>
                </div>

                <div class="card-grid">
                    <div class="card-item">
                        <div class="card-header">
                            <div class="card-label">购物卡</div>
                            <div class="card-status status-normal">正常</div>
                        </div>
                        <div class="card-number">
                            <span id="cardNumber1234">**** **** **** 1234</span>
                            <button class="eye-icon" onclick="toggleCardSensitiveInfo('1234')" title="查看完整卡号">👁️</button>
                        </div>
                        <div class="card-info">
                            <div class="card-info-item">
                                <div class="card-info-label">有效期</div>
                                <div class="card-info-value" id="cardExpiry1234">12/26</div>
                            </div>
                            <div class="card-info-item">
                                <div class="card-info-label">CVV</div>
                                <div class="card-info-value" id="cardCvv1234">***</div>
                            </div>
                            <div class="card-info-item">
                                <div class="card-info-label">开卡时间</div>
                                <div class="card-info-value">2024-01-15</div>
                            </div>
                        </div>
                        <div class="card-balance">$1,250.00</div>
                        <div class="card-actions">
                            <button class="btn btn-primary" onclick="showCardDetailById('1234')">详情</button>
                            <button class="btn btn-success" onclick="showCardRechargeById('1234')">充值</button>
                            <button class="btn btn-primary" onclick="showCardSettings('1234', 'limit')">限额</button>
                            <button class="btn btn-danger" onclick="showCardSettings('1234', 'freeze')">冻结</button>
                        </div>
                    </div>

                    <div class="card-item">
                        <div class="card-header">
                            <div class="card-label">订阅卡</div>
                            <div class="card-status status-frozen">已冻结</div>
                        </div>
                        <div class="card-number">
                            <span id="cardNumber5678">**** **** **** 5678</span>
                            <button class="eye-icon" onclick="toggleCardSensitiveInfo('5678')" title="查看完整卡号">👁️</button>
                        </div>
                        <div class="card-info">
                            <div class="card-info-item">
                                <div class="card-info-label">有效期</div>
                                <div class="card-info-value" id="cardExpiry5678">08/25</div>
                            </div>
                            <div class="card-info-item">
                                <div class="card-info-label">CVV</div>
                                <div class="card-info-value" id="cardCvv5678">***</div>
                            </div>
                            <div class="card-info-item">
                                <div class="card-info-label">开卡时间</div>
                                <div class="card-info-value">2023-08-20</div>
                            </div>
                        </div>
                        <div class="card-balance">$500.00</div>
                        <div class="card-actions">
                            <button class="btn btn-primary" onclick="showCardDetailById('5678')">详情</button>
                            <button class="btn btn-success" onclick="showCardSettings('5678', 'unfreeze')">解冻</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function loadPage(pageName) {
            console.log('Loading page:', pageName);
            
            // 更新菜单状态
            document.querySelectorAll('.menu-item a').forEach(link => {
                link.classList.remove('active');
            });
            
            // 找到对应的菜单项并激活
            const menuLink = document.querySelector(`.menu-item a[onclick*="loadPage('${pageName}')"]`);
            if (menuLink) {
                menuLink.classList.add('active');
            }
            
            // 更新页面标题
            const titles = {
                'card-management': { title: '卡片管理', desc: '管理您的所有UCard虚拟信用卡' },
                'card-application': { title: '开卡申请', desc: '申请新的UCard虚拟信用卡' },
                'card-recharge': { title: '卡片充值', desc: '为您的UCard充值数字货币' },
                'transaction-query': { title: '交易查询', desc: '查看您的交易记录和详情' },
                'balance-query': { title: '余额查询', desc: '查看账户余额和资金明细' },
                'notification-center': { title: '通知中心', desc: '查看系统通知和消息' }
            };
            
            const pageInfo = titles[pageName];
            if (pageInfo) {
                document.getElementById('page-title').textContent = pageInfo.title;
                document.getElementById('page-description').textContent = pageInfo.desc;
            }
            
            // 简单的页面内容切换
            const contentBody = document.getElementById('content-body');
            if (pageName === 'card-management') {
                // 显示默认的卡片管理内容
                contentBody.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3 style="margin: 0; color: #1e293b; font-size: 18px; font-weight: 600;">我的卡片</h3>
                        <button onclick="loadPage('card-application')" style="
                            background: #f59e0b;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 8px;
                            font-size: 14px;
                            font-weight: 500;
                            cursor: pointer;
                            display: flex;
                            align-items: center;
                            gap: 6px;
                        ">
                            <span>📝</span>
                            申请新卡
                        </button>
                    </div>

                    <div class="card-grid">
                        <div class="card-item">
                            <div class="card-header">
                                <div class="card-label">购物卡</div>
                                <div class="card-status status-normal">正常</div>
                            </div>
                            <div class="card-number">
                                <span id="jsCardNumber1234">**** **** **** 1234</span>
                                <button class="eye-icon" onclick="toggleCardSensitiveInfo('1234')" title="查看完整卡号">👁️</button>
                            </div>
                            <div class="card-info">
                                <div class="card-info-item">
                                    <div class="card-info-label">有效期</div>
                                    <div class="card-info-value" id="jsCardExpiry1234">12/26</div>
                                </div>
                                <div class="card-info-item">
                                    <div class="card-info-label">CVV</div>
                                    <div class="card-info-value" id="jsCardCvv1234">***</div>
                                </div>
                                <div class="card-info-item">
                                    <div class="card-info-label">开卡时间</div>
                                    <div class="card-info-value">2024-01-15</div>
                                </div>
                            </div>
                            <div class="card-balance">$1,250.00</div>
                            <div class="card-actions">
                                <button class="btn btn-primary" onclick="showCardDetailById('1234')">详情</button>
                                <button class="btn btn-success" onclick="showCardRechargeById('1234')">充值</button>
                                <button class="btn btn-primary" onclick="showCardSettings('1234', 'limit')">限额</button>
                                <button class="btn btn-danger" onclick="showCardSettings('1234', 'freeze')">冻结</button>
                            </div>
                        </div>

                        <div class="card-item">
                            <div class="card-header">
                                <div class="card-label">订阅卡</div>
                                <div class="card-status status-frozen">已冻结</div>
                            </div>
                            <div class="card-number">
                                <span id="jsCardNumber5678">**** **** **** 5678</span>
                                <button class="eye-icon" onclick="toggleCardSensitiveInfo('5678')" title="查看完整卡号">👁️</button>
                            </div>
                            <div class="card-info">
                                <div class="card-info-item">
                                    <div class="card-info-label">有效期</div>
                                    <div class="card-info-value" id="jsCardExpiry5678">08/25</div>
                                </div>
                                <div class="card-info-item">
                                    <div class="card-info-label">CVV</div>
                                    <div class="card-info-value" id="jsCardCvv5678">***</div>
                                </div>
                                <div class="card-info-item">
                                    <div class="card-info-label">开卡时间</div>
                                    <div class="card-info-value">2023-08-20</div>
                                </div>
                            </div>
                            <div class="card-balance">$500.00</div>
                            <div class="card-actions">
                                <button class="btn btn-primary" onclick="showCardDetailById('5678')">详情</button>
                                <button class="btn btn-success" onclick="showCardSettings('5678', 'unfreeze')">解冻</button>
                            </div>
                        </div>
                    </div>
                `;
                console.log('Showing card management content');
            } else if (pageName === 'card-application') {
                showCardApplication();
            } else if (pageName === 'card-recharge') {
                showCardRecharge();
            } else if (pageName === 'transaction-query') {
                showTransactionQuery();
            } else if (pageName === 'balance-query') {
                showBalanceQuery();
            } else if (pageName === 'notification-center') {
                showNotificationCenter();
            } else if (pageName === 'application-success') {
                showApplicationSuccess();
            } else if (pageName === 'recharge-success') {
                showRechargeSuccess();
            } else if (pageName === 'card-detail') {
                showCardDetail();
            } else if (pageName === 'transaction-detail') {
                showTransactionDetailPage();
            } else if (pageName === 'card-limit-settings') {
                showCardLimitSettings();
            } else {
                contentBody.innerHTML = `
                    <div style="text-align: center; padding: 60px; color: #64748b;">
                        <div style="font-size: 64px; margin-bottom: 20px;">🚧</div>
                        <h3 style="color: #1e293b; margin-bottom: 10px;">功能开发中</h3>
                        <p>该功能正在开发中，敬请期待...</p>
                        <button onclick="loadPage('card-management')" style="margin-top: 20px; padding: 12px 24px; background: #f59e0b; color: white; border: none; border-radius: 8px; cursor: pointer;">返回卡片管理</button>
                    </div>
                `;
            }
        }

        // 全局变量
        let currentStep = 1;
        let phoneVerified = false;
        let selectedAccount = '';

        // 开卡申请页面
        function showCardApplication() {
            const contentBody = document.getElementById('content-body');
            contentBody.innerHTML = `
                <style>
                    .step-indicator { display: flex; justify-content: center; margin-bottom: 40px; }
                    .step { display: flex; align-items: center; color: #64748b; font-size: 14px; font-weight: 500; }
                    .step.active { color: #f59e0b; }
                    .step.completed { color: #10b981; }
                    .step-number { width: 32px; height: 32px; border-radius: 50%; background: #e2e8f0; color: #64748b; display: flex; align-items: center; justify-content: center; margin-right: 12px; font-weight: 600; border: 2px solid #e2e8f0; }
                    .step.active .step-number { background: #f59e0b; color: white; border-color: #f59e0b; }
                    .step.completed .step-number { background: #10b981; color: white; border-color: #10b981; }
                    .step-connector { width: 60px; height: 2px; background: #e2e8f0; margin: 0 20px; }
                    .step.completed + .step .step-connector { background: #10b981; }
                    .step-content { display: none; }
                    .step-content.active { display: block; }
                    .form-section { padding: 30px; border-bottom: 1px solid #e2e8f0; }
                    .form-section:last-child { border-bottom: none; }
                    .section-title { font-size: 20px; font-weight: 600; color: #1e293b; margin-bottom: 20px; display: flex; align-items: center; }
                    .section-title .icon { margin-right: 10px; font-size: 24px; }
                    .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
                    .form-group { margin-bottom: 20px; }
                    .form-group label { display: block; margin-bottom: 8px; font-weight: 500; color: #1e293b; }
                    .form-group input, .form-group select { width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; }
                    .form-group input:focus, .form-group select:focus { outline: none; border-color: #f59e0b; box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2); }
                    .form-group small { color: #64748b; font-size: 12px; margin-top: 5px; display: block; }
                    .quick-select { display: flex; gap: 10px; margin-top: 10px; flex-wrap: wrap; }
                    .quick-btn { padding: 8px 16px; border: 1px solid #d1d5db; background: white; border-radius: 20px; font-size: 12px; cursor: pointer; transition: all 0.3s ease; }
                    .quick-btn:hover, .quick-btn.active { background: #f59e0b; color: white; border-color: #f59e0b; }
                    .account-selector { border: 1px solid #d1d5db; border-radius: 8px; padding: 15px; margin-bottom: 15px; cursor: pointer; transition: all 0.3s ease; }
                    .account-selector:hover, .account-selector.selected { border-color: #f59e0b; background: #fefbf3; }
                    .account-info { display: flex; justify-content: space-between; align-items: center; }
                    .account-type { font-weight: 500; color: #1e293b; }
                    .account-balance { font-size: 14px; color: #10b981; font-weight: 600; }
                    .calculation-box { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin-top: 20px; }
                    .calc-row { display: flex; justify-content: space-between; margin-bottom: 10px; font-size: 14px; }
                    .calc-row.total { border-top: 1px solid #d1d5db; padding-top: 10px; font-weight: 600; font-size: 16px; color: #1e293b; }
                    .calc-label { color: #64748b; }
                    .calc-value { font-weight: 500; }
                    .btn-group { display: flex; gap: 15px; justify-content: flex-end; margin-top: 30px; }
                    .btn { padding: 12px 24px; border: none; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; transition: all 0.3s ease; }
                    .btn-secondary { background: #6b7280; color: white; }
                    .btn-primary { background: #f59e0b; color: white; }
                    .btn:hover { transform: translateY(-1px); }
                    .btn:disabled { opacity: 0.5; cursor: not-allowed; transform: none; }
                    .phone-verification { display: flex; gap: 10px; align-items: center; }
                    .phone-verification input { flex: 1; }
                    .btn-verify { padding: 12px 20px; background: #f59e0b; color: white; border: none; border-radius: 6px; font-size: 14px; cursor: pointer; white-space: nowrap; }
                    .verification-status { margin-top: 10px; padding: 10px; border-radius: 6px; font-size: 14px; }
                    .status-success { background: #d1fae5; color: #065f46; border: 1px solid #a7f3d0; }
                </style>

                <div class="step-indicator">
                    <div class="step active" id="step1">
                        <div class="step-number">1</div>
                        <span>用卡人信息</span>
                    </div>
                    <div class="step-connector"></div>
                    <div class="step" id="step2">
                        <div class="step-number">2</div>
                        <span>充值设置</span>
                    </div>
                </div>

                <form id="applicationForm">
                    <!-- 第一步：用卡人信息 -->
                    <div class="step-content active" id="step1Content">
                        <div class="form-section">
                            <h3 class="section-title">
                                <span class="icon">📝</span>
                                用卡人信息
                            </h3>

                            <div class="form-group">
                                <label for="cardLabel">卡片标签 <small>(可选)</small></label>
                                <input type="text" id="cardLabel" placeholder="如：购物卡、订阅卡等">
                                <small>为您的卡片设置一个便于识别的标签</small>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="cardholderName">用卡人姓名</label>
                                    <select id="cardholderName" onchange="handleNameChange()">
                                        <option value="">请选择或输入用卡人姓名</option>
                                        <option value="John Smith">John Smith (KYC信息)</option>
                                        <option value="Jane Doe">Jane Doe (常用)</option>
                                        <option value="custom">手动输入...</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="customName" id="customNameLabel" style="display: none;">自定义姓名</label>
                                    <input type="text" id="customName" placeholder="输入用卡人姓名" style="display: none;">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="phoneNumber">用卡人手机号</label>
                                <div class="phone-verification">
                                    <select id="phoneSelect" onchange="handlePhoneChange()">
                                        <option value="">选择已验证手机号或输入新号码</option>
                                        <option value="+1234567890">******-567-890 (已验证)</option>
                                        <option value="+9876543210">+98 765-432-10 (已验证)</option>
                                        <option value="new">输入新手机号...</option>
                                    </select>
                                    <button type="button" class="btn-verify" id="verifyBtn" style="display: none;" onclick="sendVerification()">发送验证码</button>
                                </div>
                                <input type="text" id="newPhone" placeholder="输入新手机号" style="display: none; margin-top: 10px;">
                                <input type="text" id="verificationCode" placeholder="输入验证码" style="display: none; margin-top: 10px;">
                                <div id="verificationStatus" class="verification-status" style="display: none;"></div>
                                <small>用于3DS验证，确保交易安全</small>
                            </div>


                        </div>

                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" onclick="nextStep()">下一步</button>
                        </div>
                    </div>

                    <!-- 第二步：充值设置 -->
                    <div class="step-content" id="step2Content">
                        <div class="form-section">
                            <h3 class="section-title">
                                <span class="icon">💰</span>
                                初始充值信息
                            </h3>

                            <div class="form-group">
                                <label for="rechargeAmount">充值金额 (USD)</label>
                                <input type="number" id="rechargeAmount" placeholder="输入充值金额" min="10" step="0.01" oninput="calculateTotal()">
                                <div class="quick-select">
                                    <button type="button" class="quick-btn" onclick="selectAmount(100)">$100</button>
                                    <button type="button" class="quick-btn" onclick="selectAmount(500)">$500</button>
                                    <button type="button" class="quick-btn" onclick="selectAmount(1000)">$1000</button>
                                </div>
                                <small>最低充值金额 $10</small>
                            </div>

                            <div class="form-group">
                                <label>选择扣款账户</label>
                                <div class="account-selector" onclick="selectAccount('usdt')">
                                    <div class="account-info">
                                        <span class="account-type">USDT 账户</span>
                                        <span class="account-balance">余额: 2,500.00 USDT</span>
                                    </div>
                                </div>
                                <div class="account-selector" onclick="selectAccount('usdc')">
                                    <div class="account-info">
                                        <span class="account-type">USDC 账户</span>
                                        <span class="account-balance">余额: 1,800.50 USDC</span>
                                    </div>
                                </div>
                            </div>

                            <div class="calculation-box" id="calculationBox">
                                <div class="calc-row">
                                    <span class="calc-label">充值金额:</span>
                                    <span class="calc-value" id="chargeAmount">$0.00</span>
                                </div>
                                <div class="calc-row">
                                    <span class="calc-label">充值手续费 (2%):</span>
                                    <span class="calc-value" id="chargeFee">$0.00</span>
                                </div>
                                <div class="calc-row">
                                    <span class="calc-label">开卡费用:</span>
                                    <span class="calc-value">$5.00</span>
                                </div>
                                <div class="calc-row" id="exchangeRateRow" style="display: none;">
                                    <span class="calc-label">兑换率:</span>
                                    <span class="calc-value" id="exchangeRate">1 USDT = 1.0002 USD</span>
                                </div>
                                <div class="calc-row total">
                                    <span class="calc-label">总计扣款:</span>
                                    <span class="calc-value" id="totalAmount">0.00 USDT</span>
                                </div>
                            </div>
                        </div>

                        <div class="btn-group">
                            <button type="button" class="btn btn-secondary" onclick="prevStep()">上一步</button>
                            <button type="button" class="btn btn-primary" id="submitBtn" onclick="submitApplication()" disabled>确认申请开卡</button>
                        </div>
                    </div>
                </form>
            `;
        }

        // 开卡申请相关函数
        function handleNameChange() {
            const cardholderName = document.getElementById('cardholderName').value;
            const customNameGroup = document.getElementById('customName');
            const customNameLabel = document.getElementById('customNameLabel');

            if (cardholderName === 'custom') {
                customNameGroup.style.display = 'block';
                customNameLabel.style.display = 'block';
            } else {
                customNameGroup.style.display = 'none';
                customNameLabel.style.display = 'none';
            }
        }

        function handlePhoneChange() {
            const phoneSelect = document.getElementById('phoneSelect').value;
            const newPhoneInput = document.getElementById('newPhone');
            const verifyBtn = document.getElementById('verifyBtn');
            const verificationCode = document.getElementById('verificationCode');
            const verificationStatus = document.getElementById('verificationStatus');

            if (phoneSelect === 'new') {
                newPhoneInput.style.display = 'block';
                verifyBtn.style.display = 'block';
                phoneVerified = false;
            } else if (phoneSelect) {
                newPhoneInput.style.display = 'none';
                verifyBtn.style.display = 'none';
                verificationCode.style.display = 'none';
                verificationStatus.style.display = 'none';
                phoneVerified = true;
            } else {
                newPhoneInput.style.display = 'none';
                verifyBtn.style.display = 'none';
                verificationCode.style.display = 'none';
                verificationStatus.style.display = 'none';
                phoneVerified = false;
            }
        }

        function sendVerification() {
            const newPhone = document.getElementById('newPhone').value;
            if (!newPhone) {
                showToast('请输入手机号', 'warning');
                return;
            }

            document.getElementById('verificationCode').style.display = 'block';
            document.getElementById('verificationStatus').style.display = 'block';
            document.getElementById('verificationStatus').className = 'verification-status status-success';
            document.getElementById('verificationStatus').textContent = '验证码已发送，请查收短信';

            // 模拟验证码验证
            document.getElementById('verificationCode').addEventListener('input', function() {
                if (this.value.length === 6) {
                    phoneVerified = true;
                    document.getElementById('verificationStatus').textContent = '手机号验证成功';
                }
            });
        }

        function selectAmount(amount) {
            document.getElementById('rechargeAmount').value = amount;
            document.querySelectorAll('.quick-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            calculateTotal();
        }

        function selectAccount(type) {
            selectedAccount = type;
            document.querySelectorAll('.account-selector').forEach(selector => {
                selector.classList.remove('selected');
            });
            event.target.closest('.account-selector').classList.add('selected');
            calculateTotal();
        }

        function calculateTotal() {
            const rechargeAmount = parseFloat(document.getElementById('rechargeAmount').value) || 0;
            const chargeFee = rechargeAmount * 0.02;
            const openCardFee = 5.00;
            const totalUSD = rechargeAmount + chargeFee + openCardFee;

            document.getElementById('chargeAmount').textContent = `$${rechargeAmount.toFixed(2)}`;
            document.getElementById('chargeFee').textContent = `$${chargeFee.toFixed(2)}`;

            if (selectedAccount) {
                const exchangeRates = getExchangeRates();
                const rate = exchangeRates[selectedAccount];
                const totalCrypto = totalUSD / rate.toUSD;

                // 显示兑换率
                document.getElementById('exchangeRateRow').style.display = 'flex';
                document.getElementById('exchangeRate').textContent = `1 ${selectedAccount.toUpperCase()} = ${rate.toUSD.toFixed(4)} USD`;
                document.getElementById('totalAmount').textContent = `${totalCrypto.toFixed(4)} ${selectedAccount.toUpperCase()}`;
            } else {
                document.getElementById('exchangeRateRow').style.display = 'none';
                document.getElementById('totalAmount').textContent = '请选择扣款账户';
            }

            checkFormValidity();
        }

        function checkFormValidity() {
            const rechargeAmount = parseFloat(document.getElementById('rechargeAmount').value) || 0;
            const amountValid = rechargeAmount >= 10;
            const accountValid = selectedAccount !== '';

            const isValid = amountValid && accountValid;
            const submitBtn = document.getElementById('submitBtn');
            if (submitBtn) {
                submitBtn.disabled = !isValid;
            }
        }

        function nextStep() {
            const cardholderName = document.getElementById('cardholderName').value;
            const customName = document.getElementById('customName').value;

            const nameValid = cardholderName && (cardholderName !== 'custom' || customName);

            if (!nameValid) {
                showToast('请填写用卡人姓名', 'warning');
                return;
            }

            if (!phoneVerified) {
                showToast('请验证手机号', 'warning');
                return;
            }

            currentStep = 2;
            updateStepDisplay();
        }

        function prevStep() {
            currentStep = 1;
            updateStepDisplay();
        }

        function updateStepDisplay() {
            for (let i = 1; i <= 2; i++) {
                const step = document.getElementById(`step${i}`);
                const content = document.getElementById(`step${i}Content`);

                if (i < currentStep) {
                    step.className = 'step completed';
                } else if (i === currentStep) {
                    step.className = 'step active';
                } else {
                    step.className = 'step';
                }

                if (content) {
                    content.className = i === currentStep ? 'step-content active' : 'step-content';
                }
            }
        }

        function submitApplication() {
            const rechargeAmount = parseFloat(document.getElementById('rechargeAmount').value) || 0;
            if (rechargeAmount < 10) {
                showToast('充值金额不能少于$10', 'warning');
                return;
            }

            if (!selectedAccount) {
                showToast('请选择扣款账户', 'warning');
                return;
            }

            const applicationData = {
                cardLabel: document.getElementById('cardLabel').value || '默认卡片',
                cardholderName: document.getElementById('cardholderName').value === 'custom'
                    ? document.getElementById('customName').value
                    : document.getElementById('cardholderName').value,
                phoneNumber: document.getElementById('phoneSelect').value === 'new'
                    ? document.getElementById('newPhone').value
                    : document.getElementById('phoneSelect').value,
                rechargeAmount: document.getElementById('rechargeAmount').value,
                rechargeFee: (parseFloat(document.getElementById('rechargeAmount').value) * 0.02).toFixed(2),
                selectedAccount: selectedAccount,
                totalDeducted: document.getElementById('totalAmount').textContent
            };

            localStorage.setItem('applicationData', JSON.stringify(applicationData));

            // 安全验证
            showSecurityVerification('card-application', () => {
                document.getElementById('submitBtn').textContent = '处理中...';
                document.getElementById('submitBtn').disabled = true;

                setTimeout(() => {
                    loadPage('application-success');
                }, 2000);
            });
        }

        // 申请成功页面
        function showApplicationSuccess() {
            const contentBody = document.getElementById('content-body');
            const applicationData = JSON.parse(localStorage.getItem('applicationData') || '{}');

            contentBody.innerHTML = `
                <style>
                    .success-container { max-width: 600px; margin: 0 auto; text-align: center; }
                    .success-icon { width: 80px; height: 80px; background: #10b981; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 32px; font-size: 40px; color: white; animation: successPulse 2s ease-in-out infinite; }
                    @keyframes successPulse { 0% { transform: scale(1); } 50% { transform: scale(1.1); } 100% { transform: scale(1); } }
                    .success-title { font-size: 32px; font-weight: 700; color: #1e293b; margin-bottom: 16px; }
                    .success-subtitle { font-size: 18px; color: #64748b; margin-bottom: 40px; line-height: 1.5; }
                    .card-preview { background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%); border-radius: 16px; padding: 32px; margin: 40px 0; color: white; }
                    .card-preview-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
                    .card-label { font-size: 18px; font-weight: 600; }
                    .card-type { font-size: 14px; opacity: 0.9; }
                    .card-number { font-size: 24px; font-weight: 600; letter-spacing: 3px; margin-bottom: 20px; font-family: 'Courier New', monospace; }
                    .card-details { display: flex; justify-content: space-between; align-items: center; }
                    .card-detail-item { text-align: left; }
                    .card-detail-label { font-size: 12px; opacity: 0.8; margin-bottom: 4px; }
                    .card-detail-value { font-size: 16px; font-weight: 600; }
                    .card-balance { text-align: right; }
                    .balance-label { font-size: 12px; opacity: 0.8; margin-bottom: 4px; }
                    .balance-amount { font-size: 28px; font-weight: 700; }
                    .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 40px 0; }
                    .info-card { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 24px; text-align: left; }
                    .info-title { font-size: 16px; font-weight: 600; color: #1e293b; margin-bottom: 12px; }
                    .info-item { display: flex; justify-content: space-between; margin-bottom: 8px; font-size: 14px; }
                    .info-label { color: #64748b; }
                    .info-value { color: #1e293b; font-weight: 500; }
                    .action-buttons { display: flex; gap: 16px; justify-content: center; margin-top: 40px; }
                    .btn { padding: 14px 28px; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.2s ease; text-decoration: none; display: inline-flex; align-items: center; gap: 8px; }
                    .btn-primary { background: #f59e0b; color: white; }
                    .btn-primary:hover { background: #d97706; transform: translateY(-2px); }
                    .btn-secondary { background: #f8fafc; color: #1e293b; border: 1px solid #e2e8f0; }
                    .btn-secondary:hover { background: #f1f5f9; transform: translateY(-2px); }
                </style>

                <div class="success-container">
                    <div class="success-icon">✓</div>

                    <h1 class="success-title">开卡申请成功！</h1>
                    <p class="success-subtitle">
                        恭喜您！您的UCard虚拟信用卡已成功创建并完成初始充值。<br>
                        您现在可以开始使用这张卡片进行线上消费了。
                    </p>

                    <div class="card-preview">
                        <div class="card-preview-header">
                            <div class="card-label">${applicationData.cardLabel || '默认卡片'}</div>
                            <div class="card-type">Virtual Card</div>
                        </div>
                        <div class="card-number">4532 1234 5678 9876</div>
                        <div class="card-details">
                            <div class="card-detail-item">
                                <div class="card-detail-label">有效期</div>
                                <div class="card-detail-value">12/27</div>
                            </div>
                            <div class="card-detail-item">
                                <div class="card-detail-label">CVV</div>
                                <div class="card-detail-value">***</div>
                            </div>
                            <div class="card-balance">
                                <div class="balance-label">当前余额</div>
                                <div class="balance-amount">$${applicationData.rechargeAmount || '0.00'}</div>
                            </div>
                        </div>
                    </div>

                    <div class="info-grid">
                        <div class="info-card">
                            <div class="info-title">申请信息</div>
                            <div class="info-item">
                                <span class="info-label">申请时间:</span>
                                <span class="info-value">${new Date().toLocaleString('zh-CN')}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">用卡人:</span>
                                <span class="info-value">${applicationData.cardholderName || 'N/A'}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">手机号:</span>
                                <span class="info-value">${applicationData.phoneNumber || 'N/A'}</span>
                            </div>
                        </div>

                        <div class="info-card">
                            <div class="info-title">费用明细</div>
                            <div class="info-item">
                                <span class="info-label">充值金额:</span>
                                <span class="info-value">$${applicationData.rechargeAmount || '0.00'}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">充值手续费:</span>
                                <span class="info-value">$${applicationData.rechargeFee || '0.00'}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">开卡费用:</span>
                                <span class="info-value">$5.00</span>
                            </div>
                            <div class="info-item" style="border-top: 1px solid #e2e8f0; padding-top: 8px; margin-top: 8px;">
                                <span class="info-label">总计扣款:</span>
                                <span class="info-value">${applicationData.totalDeducted || 'N/A'}</span>
                            </div>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <a href="#" class="btn btn-primary" onclick="loadPage('card-management')">
                            💳 查看卡片详情
                        </a>
                        <a href="#" class="btn btn-secondary" onclick="loadPage('card-recharge')">
                            💰 继续充值
                        </a>
                    </div>
                </div>
            `;
        }

        // 卡片充值页面
        function showCardRecharge() {
            const contentBody = document.getElementById('content-body');
            const preSelectedCardId = localStorage.getItem('selectedCardId');

            // 如果有预选卡片，直接跳过卡片选择步骤
            if (preSelectedCardId) {
                const cardData = getCardData(preSelectedCardId);
                showRechargeForm(preSelectedCardId, cardData.label, cardData.number, cardData.balance);
                return;
            }

            contentBody.innerHTML = `
                <style>
                    .recharge-container { max-width: 800px; margin: 0 auto; }
                    .card-selector { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
                    .card-option { border: 2px solid #e2e8f0; border-radius: 12px; padding: 24px; cursor: pointer; transition: all 0.2s ease; background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%); color: white; position: relative; box-shadow: 0 4px 12px 0 rgba(245, 158, 11, 0.25); }
                    .card-option:hover { transform: translateY(-2px); box-shadow: 0 12px 24px 0 rgba(245, 158, 11, 0.4); }
                    .card-option.selected { border-color: #f59e0b; box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.2); }
                    .card-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
                    .card-label { font-size: 16px; font-weight: 600; }
                    .card-status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 500; background: rgba(16, 185, 129, 0.2); color: #10b981; }
                    .card-number { font-size: 16px; font-weight: 500; letter-spacing: 1px; margin-bottom: 10px; font-family: 'Courier New', monospace; }
                    .card-balance { font-size: 20px; font-weight: 700; text-align: right; }
                    .recharge-section { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 30px; margin-top: 30px; display: none; }
                    .recharge-section.active { display: block; }
                    .form-group { margin-bottom: 20px; }
                    .form-group label { display: block; margin-bottom: 8px; font-weight: 500; color: #1e293b; }
                    .form-group input { width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; }
                    .quick-select { display: flex; gap: 10px; margin-top: 10px; flex-wrap: wrap; }
                    .quick-btn { padding: 8px 16px; border: 1px solid #d1d5db; background: white; border-radius: 20px; font-size: 12px; cursor: pointer; transition: all 0.3s ease; }
                    .quick-btn:hover, .quick-btn.active { background: #f59e0b; color: white; border-color: #f59e0b; }
                    .account-selector { border: 1px solid #d1d5db; border-radius: 8px; padding: 15px; margin-bottom: 15px; cursor: pointer; transition: all 0.3s ease; }
                    .account-selector:hover, .account-selector.selected { border-color: #f59e0b; background: #fefbf3; }
                    .account-info { display: flex; justify-content: space-between; align-items: center; }
                    .account-type { font-weight: 500; color: #1e293b; }
                    .account-balance { font-size: 14px; color: #10b981; font-weight: 600; }
                    .calculation-box { background: #ffffff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin-top: 20px; }
                    .calc-row { display: flex; justify-content: space-between; margin-bottom: 10px; font-size: 14px; }
                    .calc-row.total { border-top: 1px solid #d1d5db; padding-top: 10px; font-weight: 600; font-size: 16px; color: #1e293b; }
                    .calc-label { color: #64748b; }
                    .calc-value { font-weight: 500; }
                    .btn-submit { background: #f59e0b; color: white; border: none; padding: 15px 40px; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: transform 0.3s ease; min-width: 200px; margin-top: 20px; }
                    .btn-submit:hover { transform: translateY(-2px); }
                    .btn-submit:disabled { background: #9ca3af; cursor: not-allowed; transform: none; }
                </style>

                <div class="recharge-container">
                    <h3 style="font-size: 20px; font-weight: 600; color: #1e293b; margin-bottom: 20px;">选择充值卡片</h3>

                    <div class="card-selector">
                        <div class="card-option" onclick="selectRechargeCard('1234', '购物卡', '**** **** **** 1234', '$1,250.00')">
                            <div class="card-header">
                                <div class="card-label">购物卡</div>
                                <div class="card-status">正常</div>
                            </div>
                            <div class="card-number">**** **** **** 1234</div>
                            <div class="card-balance">$1,250.00</div>
                        </div>

                        <div class="card-option" onclick="selectRechargeCard('5678', '订阅卡', '**** **** **** 5678', '$500.00')">
                            <div class="card-header">
                                <div class="card-label">订阅卡</div>
                                <div class="card-status">正常</div>
                            </div>
                            <div class="card-number">**** **** **** 5678</div>
                            <div class="card-balance">$500.00</div>
                        </div>
                    </div>

                    <div class="recharge-section" id="rechargeSection">
                        <h3 style="font-size: 20px; font-weight: 600; color: #1e293b; margin-bottom: 20px;">充值信息</h3>

                        <div class="form-group">
                            <label for="rechargeAmount">充值金额 (USD)</label>
                            <input type="number" id="rechargeAmount" placeholder="输入充值金额" min="10" step="0.01" oninput="calculateRechargeTotal()">
                            <div class="quick-select">
                                <button type="button" class="quick-btn" onclick="selectRechargeAmount(100)">$100</button>
                                <button type="button" class="quick-btn" onclick="selectRechargeAmount(500)">$500</button>
                                <button type="button" class="quick-btn" onclick="selectRechargeAmount(1000)">$1000</button>
                            </div>
                            <small style="color: #64748b; font-size: 12px; margin-top: 5px; display: block;">最低充值金额 $10</small>
                        </div>

                        <div class="form-group">
                            <label>选择扣款账户</label>
                            <div class="account-selector" onclick="selectRechargeAccount('usdt')">
                                <div class="account-info">
                                    <span class="account-type">USDT 账户</span>
                                    <span class="account-balance">余额: 2,500.00 USDT</span>
                                </div>
                            </div>
                            <div class="account-selector" onclick="selectRechargeAccount('usdc')">
                                <div class="account-info">
                                    <span class="account-type">USDC 账户</span>
                                    <span class="account-balance">余额: 1,800.50 USDC</span>
                                </div>
                            </div>
                        </div>

                        <div class="calculation-box">
                            <div class="calc-row">
                                <span class="calc-label">充值金额:</span>
                                <span class="calc-value" id="rechargeChargeAmount">$0.00</span>
                            </div>
                            <div class="calc-row">
                                <span class="calc-label">充值手续费 (2%):</span>
                                <span class="calc-value" id="rechargeChargeFee">$0.00</span>
                            </div>
                            <div class="calc-row" id="rechargeExchangeRateRow" style="display: none;">
                                <span class="calc-label">兑换率:</span>
                                <span class="calc-value" id="rechargeExchangeRate">1 USDT = 1.0002 USD</span>
                            </div>
                            <div class="calc-row total">
                                <span class="calc-label">总计扣款:</span>
                                <span class="calc-value" id="rechargeTotalAmount">0.00 USDT</span>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <button class="btn-submit" id="rechargeSubmitBtn" onclick="submitRecharge()" disabled>确认充值</button>
                        </div>
                    </div>
                </div>
            `;
        }

        // 显示充值表单（跳过卡片选择）
        function showRechargeForm(cardId, cardLabel, cardNumber, cardBalance) {
            const contentBody = document.getElementById('content-body');
            selectedRechargeCard = cardId;

            contentBody.innerHTML = `
                <style>
                    .recharge-container { max-width: 800px; margin: 0 auto; }
                    .selected-card { background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%); border-radius: 12px; padding: 24px; color: white; margin-bottom: 30px; box-shadow: 0 4px 12px 0 rgba(245, 158, 11, 0.25); }
                    .card-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
                    .card-label { font-size: 18px; font-weight: 600; }
                    .card-status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 500; background: rgba(16, 185, 129, 0.2); color: #10b981; }
                    .card-number { font-size: 16px; font-weight: 500; letter-spacing: 1px; margin-bottom: 10px; font-family: 'Courier New', monospace; }
                    .card-balance { font-size: 20px; font-weight: 700; text-align: right; }
                    .recharge-section { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 30px; }
                    .form-group { margin-bottom: 20px; }
                    .form-group label { display: block; margin-bottom: 8px; font-weight: 500; color: #1e293b; }
                    .form-group input { width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; }
                    .quick-select { display: flex; gap: 10px; margin-top: 10px; flex-wrap: wrap; }
                    .quick-btn { padding: 8px 16px; border: 1px solid #d1d5db; background: white; border-radius: 20px; font-size: 12px; cursor: pointer; transition: all 0.3s ease; }
                    .quick-btn:hover, .quick-btn.active { background: #f59e0b; color: white; border-color: #f59e0b; }
                    .account-selector { border: 1px solid #d1d5db; border-radius: 8px; padding: 15px; margin-bottom: 15px; cursor: pointer; transition: all 0.3s ease; }
                    .account-selector:hover, .account-selector.selected { border-color: #f59e0b; background: #fefbf3; }
                    .account-info { display: flex; justify-content: space-between; align-items: center; }
                    .account-type { font-weight: 500; color: #1e293b; }
                    .account-balance { font-size: 14px; color: #10b981; font-weight: 600; }
                    .calculation-box { background: #ffffff; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin-top: 20px; }
                    .calc-row { display: flex; justify-content: space-between; margin-bottom: 10px; font-size: 14px; }
                    .calc-row.total { border-top: 1px solid #d1d5db; padding-top: 10px; font-weight: 600; font-size: 16px; color: #1e293b; }
                    .calc-label { color: #64748b; }
                    .calc-value { font-weight: 500; }
                    .btn-submit { background: #f59e0b; color: white; border: none; padding: 15px 40px; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: transform 0.3s ease; min-width: 200px; margin-top: 20px; }
                    .btn-submit:hover { transform: translateY(-2px); }
                    .btn-submit:disabled { background: #9ca3af; cursor: not-allowed; transform: none; }
                    .btn-back { background: #6b7280; color: white; border: none; padding: 10px 20px; border-radius: 6px; font-size: 14px; cursor: pointer; margin-bottom: 20px; }
                </style>

                <div class="recharge-container">
                    <button class="btn-back" onclick="clearSelectedCard(); loadPage('card-recharge')">← 重新选择卡片</button>

                    <div class="selected-card">
                        <div class="card-header">
                            <div class="card-label">${cardLabel}</div>
                            <div class="card-status">正常</div>
                        </div>
                        <div class="card-number">${cardNumber}</div>
                        <div class="card-balance">${cardBalance}</div>
                    </div>

                    <div class="recharge-section">
                        <h3 style="font-size: 20px; font-weight: 600; color: #1e293b; margin-bottom: 20px;">充值信息</h3>

                        <div class="form-group">
                            <label for="rechargeAmount">充值金额 (USD)</label>
                            <input type="number" id="rechargeAmount" placeholder="输入充值金额" min="10" step="0.01" oninput="calculateRechargeTotal()">
                            <div class="quick-select">
                                <button type="button" class="quick-btn" onclick="selectRechargeAmount(100)">$100</button>
                                <button type="button" class="quick-btn" onclick="selectRechargeAmount(500)">$500</button>
                                <button type="button" class="quick-btn" onclick="selectRechargeAmount(1000)">$1000</button>
                            </div>
                            <small style="color: #64748b; font-size: 12px; margin-top: 5px; display: block;">最低充值金额 $10</small>
                        </div>

                        <div class="form-group">
                            <label>选择扣款账户</label>
                            <div class="account-selector" onclick="selectRechargeAccount('usdt')">
                                <div class="account-info">
                                    <span class="account-type">USDT 账户</span>
                                    <span class="account-balance">余额: 2,500.00 USDT</span>
                                </div>
                            </div>
                            <div class="account-selector" onclick="selectRechargeAccount('usdc')">
                                <div class="account-info">
                                    <span class="account-type">USDC 账户</span>
                                    <span class="account-balance">余额: 1,800.50 USDC</span>
                                </div>
                            </div>
                        </div>

                        <div class="calculation-box">
                            <div class="calc-row">
                                <span class="calc-label">充值金额:</span>
                                <span class="calc-value" id="rechargeChargeAmount">$0.00</span>
                            </div>
                            <div class="calc-row">
                                <span class="calc-label">充值手续费 (2%):</span>
                                <span class="calc-value" id="rechargeChargeFee">$0.00</span>
                            </div>
                            <div class="calc-row" id="rechargeExchangeRateRow" style="display: none;">
                                <span class="calc-label">兑换率:</span>
                                <span class="calc-value" id="rechargeExchangeRate">1 USDT = 1.0002 USD</span>
                            </div>
                            <div class="calc-row total">
                                <span class="calc-label">总计扣款:</span>
                                <span class="calc-value" id="rechargeTotalAmount">0.00 USDT</span>
                            </div>
                        </div>

                        <div style="text-align: center;">
                            <button class="btn-submit" id="rechargeSubmitBtn" onclick="submitRecharge()" disabled>确认充值</button>
                        </div>
                    </div>
                </div>
            `;
        }

        function showTransactionQuery() {
            const contentBody = document.getElementById('content-body');
            contentBody.innerHTML = `
                <style>
                    .transaction-container { max-width: 1000px; margin: 0 auto; }
                    .filter-section { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 24px; margin-bottom: 24px; }
                    .filter-row { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
                    .filter-group { }
                    .filter-group label { display: block; margin-bottom: 8px; font-weight: 500; color: #1e293b; font-size: 14px; }
                    .filter-group select, .filter-group input { width: 100%; padding: 10px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; }
                    .btn-filter { background: #f59e0b; color: white; border: none; padding: 10px 20px; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; }
                    .transaction-list { background: white; border: 1px solid #e2e8f0; border-radius: 12px; overflow: hidden; }
                    .transaction-header { background: #f8fafc; padding: 16px 24px; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #1e293b; display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr 100px; gap: 20px; font-size: 14px; }
                    .transaction-item { padding: 16px 24px; border-bottom: 1px solid #f1f5f9; display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr 100px; gap: 20px; align-items: center; transition: background 0.2s ease; }
                    .transaction-item:hover { background: #f8fafc; }
                    .transaction-item:last-child { border-bottom: none; }
                    .transaction-type { font-weight: 500; color: #1e293b; }
                    .transaction-amount { font-weight: 600; }
                    .transaction-amount.positive { color: #10b981; }
                    .transaction-amount.negative { color: #ef4444; }
                    .transaction-status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 500; text-align: center; }
                    .status-success { background: #d1fae5; color: #065f46; }
                    .status-pending { background: #fef3c7; color: #92400e; }
                    .status-failed { background: #fee2e2; color: #991b1b; }
                    .transaction-date { color: #64748b; font-size: 14px; }
                    .transaction-merchant { color: #64748b; font-size: 14px; }
                    .btn-detail { background: #f59e0b; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; cursor: pointer; }
                    .empty-state { text-align: center; padding: 60px 20px; color: #64748b; }
                </style>

                <div class="transaction-container">
                    <div class="filter-section">
                        <h3 style="font-size: 18px; font-weight: 600; color: #1e293b; margin-bottom: 20px;">筛选条件</h3>
                        <div class="filter-row">
                            <div class="filter-group">
                                <label for="cardFilter">选择卡片</label>
                                <select id="cardFilter">
                                    <option value="">全部卡片</option>
                                    <option value="1234">购物卡 (**** 1234)</option>
                                    <option value="5678">订阅卡 (**** 5678)</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="typeFilter">交易类型</label>
                                <select id="typeFilter">
                                    <option value="">全部类型</option>
                                    <option value="payment">消费</option>
                                    <option value="refund">退款</option>
                                    <option value="recharge">充值</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="statusFilter">交易状态</label>
                                <select id="statusFilter">
                                    <option value="">全部状态</option>
                                    <option value="success">成功</option>
                                    <option value="pending">处理中</option>
                                    <option value="failed">失败</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="dateFrom">开始日期</label>
                                <input type="date" id="dateFrom">
                            </div>
                            <div class="filter-group">
                                <label for="dateTo">结束日期</label>
                                <input type="date" id="dateTo">
                            </div>
                            <div class="filter-group" style="display: flex; align-items: end;">
                                <button class="btn-filter" onclick="filterTransactions()">查询</button>
                            </div>
                        </div>
                    </div>

                    <div class="transaction-list">
                        <div class="transaction-header">
                            <div>交易类型</div>
                            <div>商户/描述</div>
                            <div>金额</div>
                            <div>状态</div>
                            <div>交易时间</div>
                            <div>操作</div>
                        </div>

                        <div class="transaction-item">
                            <div class="transaction-type">消费</div>
                            <div class="transaction-merchant">Amazon.com</div>
                            <div class="transaction-amount negative">-$89.99</div>
                            <div class="transaction-status status-success">成功</div>
                            <div class="transaction-date">2024-01-20 14:30</div>
                            <button class="btn-detail" onclick="showTransactionDetail('tx001')">详情</button>
                        </div>



                        <div class="transaction-item">
                            <div class="transaction-type">消费</div>
                            <div class="transaction-merchant">Netflix</div>
                            <div class="transaction-amount negative">-$15.99</div>
                            <div class="transaction-status status-success">成功</div>
                            <div class="transaction-date">2024-01-18 09:00</div>
                            <button class="btn-detail" onclick="showTransactionDetail('tx003')">详情</button>
                        </div>

                        <div class="transaction-item">
                            <div class="transaction-type">消费</div>
                            <div class="transaction-merchant">Steam Store</div>
                            <div class="transaction-amount negative">-$59.99</div>
                            <div class="transaction-status status-pending">处理中</div>
                            <div class="transaction-date">2024-01-17 20:45</div>
                            <button class="btn-detail" onclick="showTransactionDetail('tx004')">详情</button>
                        </div>

                        <div class="transaction-item">
                            <div class="transaction-type">退款</div>
                            <div class="transaction-merchant">Apple Store</div>
                            <div class="transaction-amount positive">+$29.99</div>
                            <div class="transaction-status status-success">成功</div>
                            <div class="transaction-date">2024-01-16 16:20</div>
                            <button class="btn-detail" onclick="showTransactionDetail('tx005')">详情</button>
                        </div>
                    </div>
                </div>
            `;
        }

        function showBalanceQuery() {
            const contentBody = document.getElementById('content-body');
            contentBody.innerHTML = `
                <style>
                    .balance-container { max-width: 1200px; margin: 0 auto; }
                    .total-balance { background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%); border-radius: 16px; padding: 32px; color: white; margin-bottom: 30px; text-align: center; box-shadow: 0 8px 24px 0 rgba(245, 158, 11, 0.3); }
                    .total-balance-label { font-size: 16px; opacity: 0.9; margin-bottom: 8px; }
                    .total-balance-amount { font-size: 48px; font-weight: 700; margin-bottom: 8px; }
                    .total-balance-change { font-size: 16px; opacity: 0.9; }
                    .cards-overview { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px; margin-bottom: 30px; }
                    .balance-card { background: white; border: 1px solid #e2e8f0; border-radius: 12px; padding: 24px; transition: all 0.2s ease; }
                    .balance-card:hover { transform: translateY(-2px); box-shadow: 0 8px 24px rgba(0,0,0,0.1); }
                    .balance-card-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px; }
                    .balance-card-label { font-size: 18px; font-weight: 600; color: #1e293b; }
                    .balance-card-status { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 500; }
                    .status-normal { background: #d1fae5; color: #065f46; }
                    .status-frozen { background: #fee2e2; color: #991b1b; }
                    .balance-card-number { font-size: 14px; color: #64748b; margin-bottom: 16px; font-family: 'Courier New', monospace; }
                    .balance-card-amount { font-size: 28px; font-weight: 700; color: #f59e0b; margin-bottom: 8px; }
                    .balance-card-change { font-size: 14px; }
                    .balance-card-change.positive { color: #10b981; }
                    .balance-card-change.negative { color: #ef4444; }
                    .filter-section { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 24px; margin-bottom: 24px; }
                    .filter-row { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
                    .filter-group { }
                    .filter-group label { display: block; margin-bottom: 8px; font-weight: 500; color: #1e293b; font-size: 14px; }
                    .filter-group select, .filter-group input { width: 100%; padding: 10px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; }
                    .btn-filter { background: #f59e0b; color: white; border: none; padding: 10px 20px; border-radius: 6px; font-size: 14px; font-weight: 500; cursor: pointer; }
                    .flow-history { background: white; border: 1px solid #e2e8f0; border-radius: 12px; overflow: hidden; }
                    .flow-header { background: #f8fafc; padding: 16px 24px; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #1e293b; display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr; gap: 20px; font-size: 14px; }
                    .flow-item { padding: 16px 24px; border-bottom: 1px solid #f1f5f9; display: grid; grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr; gap: 20px; align-items: center; transition: background 0.2s ease; }
                    .flow-item:hover { background: #f8fafc; }
                    .flow-item:last-child { border-bottom: none; }
                    .flow-type { font-weight: 500; color: #1e293b; }
                    .flow-amount { font-weight: 600; }
                    .flow-amount.positive { color: #10b981; }
                    .flow-amount.negative { color: #ef4444; }
                    .flow-date { color: #64748b; font-size: 14px; }
                    .flow-card { color: #64748b; font-size: 14px; }
                    .flow-description { color: #64748b; font-size: 14px; }
                    .flow-balance { color: #1e293b; font-weight: 500; font-size: 14px; }
                    .type-badge { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
                    .type-recharge { background: #d1fae5; color: #065f46; }
                    .type-consume { background: #fee2e2; color: #991b1b; }
                    .type-refund { background: #dbeafe; color: #1e40af; }
                    .type-fee { background: #fef3c7; color: #92400e; }
                    .type-monthly { background: #e0e7ff; color: #3730a3; }
                </style>

                <div class="balance-container">
                    <!-- 总余额显示 -->
                    <div class="total-balance">
                        <div class="total-balance-label">所有卡片总余额</div>
                        <div class="total-balance-amount">$1,750.00</div>
                        <div class="total-balance-change">本月变动: +$484.01</div>
                    </div>

                    <!-- 各卡片余额概览 -->
                    <div class="cards-overview">
                        <div class="balance-card">
                            <div class="balance-card-header">
                                <div class="balance-card-label">购物卡</div>
                                <div class="balance-card-status status-normal">正常</div>
                            </div>
                            <div class="balance-card-number">**** **** **** 1234</div>
                            <div class="balance-card-amount">$1,250.00</div>
                            <div class="balance-card-change positive">本月: +$500.00</div>
                        </div>

                        <div class="balance-card">
                            <div class="balance-card-header">
                                <div class="balance-card-label">订阅卡</div>
                                <div class="balance-card-status status-frozen">已冻结</div>
                            </div>
                            <div class="balance-card-number">**** **** **** 5678</div>
                            <div class="balance-card-amount">$500.00</div>
                            <div class="balance-card-change negative">本月: -$15.99</div>
                        </div>
                    </div>

                    <!-- 资金流水筛选 -->
                    <div class="filter-section">
                        <h3 style="font-size: 18px; font-weight: 600; color: #1e293b; margin-bottom: 20px;">资金流水查询</h3>
                        <div class="filter-row">
                            <div class="filter-group">
                                <label for="cardFilter">选择卡片</label>
                                <select id="cardFilter">
                                    <option value="">全部卡片</option>
                                    <option value="1234">购物卡 (**** 1234)</option>
                                    <option value="5678">订阅卡 (**** 5678)</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="flowTypeFilter">资金类型</label>
                                <select id="flowTypeFilter">
                                    <option value="">全部类型</option>
                                    <option value="recharge">充值记录</option>
                                    <option value="consume">消费记录</option>
                                    <option value="refund">退款记录</option>
                                    <option value="fee">交易手续费</option>
                                    <option value="monthly">月费扣除</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label for="dateFrom">开始日期</label>
                                <input type="date" id="dateFrom">
                            </div>
                            <div class="filter-group">
                                <label for="dateTo">结束日期</label>
                                <input type="date" id="dateTo">
                            </div>
                            <div class="filter-group" style="display: flex; align-items: end;">
                                <button class="btn-filter" onclick="filterBalanceFlow()">查询</button>
                            </div>
                        </div>
                    </div>

                    <!-- 资金流水记录 -->
                    <div class="flow-history">
                        <div style="padding: 20px 24px; border-bottom: 1px solid #e2e8f0;">
                            <h3 style="font-size: 20px; font-weight: 600; color: #1e293b; margin: 0;">资金明细</h3>
                        </div>

                        <div class="flow-header">
                            <div>资金类型</div>
                            <div>卡片</div>
                            <div>变动金额</div>
                            <div>描述</div>
                            <div>时间</div>
                            <div>余额</div>
                        </div>

                        <div class="flow-item">
                            <div class="flow-type">
                                <span class="type-badge type-recharge">充值记录</span>
                            </div>
                            <div class="flow-card">购物卡 (1234)</div>
                            <div class="flow-amount positive">+$500.00</div>
                            <div class="flow-description">USDT充值到账</div>
                            <div class="flow-date">2024-01-19 10:15</div>
                            <div class="flow-balance">$1,250.00</div>
                        </div>

                        <div class="flow-item">
                            <div class="flow-type">
                                <span class="type-badge type-consume">消费记录</span>
                            </div>
                            <div class="flow-card">购物卡 (1234)</div>
                            <div class="flow-amount negative">-$89.99</div>
                            <div class="flow-description">Amazon.com 购物消费</div>
                            <div class="flow-date">2024-01-18 14:30</div>
                            <div class="flow-balance">$750.00</div>
                        </div>

                        <div class="flow-item">
                            <div class="flow-type">
                                <span class="type-badge type-consume">消费记录</span>
                            </div>
                            <div class="flow-card">订阅卡 (5678)</div>
                            <div class="flow-amount negative">-$15.99</div>
                            <div class="flow-description">Netflix 订阅服务</div>
                            <div class="flow-date">2024-01-17 09:00</div>
                            <div class="flow-balance">$515.99</div>
                        </div>

                        <div class="flow-item">
                            <div class="flow-type">
                                <span class="type-badge type-refund">退款记录</span>
                            </div>
                            <div class="flow-card">购物卡 (1234)</div>
                            <div class="flow-amount positive">+$29.99</div>
                            <div class="flow-description">Apple Store 退款入账</div>
                            <div class="flow-date">2024-01-16 16:20</div>
                            <div class="flow-balance">$839.99</div>
                        </div>

                        <div class="flow-item">
                            <div class="flow-type">
                                <span class="type-badge type-fee">交易手续费</span>
                            </div>
                            <div class="flow-card">购物卡 (1234)</div>
                            <div class="flow-amount negative">-$0.50</div>
                            <div class="flow-description">小额交易处理费</div>
                            <div class="flow-date">2024-01-15 20:45</div>
                            <div class="flow-balance">$810.00</div>
                        </div>

                        <div class="flow-item">
                            <div class="flow-type">
                                <span class="type-badge type-monthly">月费扣除</span>
                            </div>
                            <div class="flow-card">订阅卡 (5678)</div>
                            <div class="flow-amount negative">-$2.00</div>
                            <div class="flow-description">卡片月度维护费</div>
                            <div class="flow-date">2024-01-01 00:00</div>
                            <div class="flow-balance">$531.99</div>
                        </div>
                    </div>
                </div>
            `;
        }

        function showNotificationCenter() {
            const contentBody = document.getElementById('content-body');
            contentBody.innerHTML = `
                <style>
                    .notification-container { max-width: 800px; margin: 0 auto; }
                    .notification-filters { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; margin-bottom: 24px; display: flex; gap: 15px; align-items: center; flex-wrap: wrap; }
                    .filter-btn { padding: 8px 16px; border: 1px solid #d1d5db; background: white; border-radius: 20px; font-size: 14px; cursor: pointer; transition: all 0.3s ease; }
                    .filter-btn.active, .filter-btn:hover { background: #f59e0b; color: white; border-color: #f59e0b; }
                    .notification-list { background: white; border: 1px solid #e2e8f0; border-radius: 12px; overflow: hidden; }
                    .notification-item { padding: 20px 24px; border-bottom: 1px solid #f1f5f9; display: flex; gap: 16px; align-items: flex-start; transition: background 0.2s ease; position: relative; }
                    .notification-item:hover { background: #f8fafc; }
                    .notification-item:last-child { border-bottom: none; }
                    .notification-item.unread { background: #fefbf3; border-left: 4px solid #f59e0b; }
                    .notification-icon { width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 18px; flex-shrink: 0; }
                    .icon-success { background: #d1fae5; color: #065f46; }
                    .icon-warning { background: #fef3c7; color: #92400e; }
                    .icon-info { background: #dbeafe; color: #1e40af; }
                    .icon-error { background: #fee2e2; color: #991b1b; }
                    .notification-content { flex: 1; }
                    .notification-title { font-size: 16px; font-weight: 600; color: #1e293b; margin-bottom: 4px; }
                    .notification-message { font-size: 14px; color: #64748b; line-height: 1.5; margin-bottom: 8px; }
                    .notification-time { font-size: 12px; color: #9ca3af; }
                    .notification-actions { display: flex; gap: 8px; margin-top: 12px; }
                    .btn-action { padding: 6px 12px; border: 1px solid #d1d5db; background: white; border-radius: 4px; font-size: 12px; cursor: pointer; transition: all 0.2s ease; }
                    .btn-action:hover { background: #f3f4f6; }
                    .btn-primary { background: #f59e0b; color: white; border-color: #f59e0b; }
                    .btn-primary:hover { background: #d97706; }
                    .unread-badge { position: absolute; top: 20px; right: 24px; width: 8px; height: 8px; background: #f59e0b; border-radius: 50%; }
                    .empty-state { text-align: center; padding: 60px 20px; color: #64748b; }
                </style>

                <div class="notification-container">
                    <div class="notification-filters">
                        <span style="font-weight: 500; color: #1e293b;">筛选:</span>
                        <button class="filter-btn active" onclick="filterNotifications('all')">全部</button>
                        <button class="filter-btn" onclick="filterNotifications('unread')">未读</button>
                        <button class="filter-btn" onclick="filterNotifications('transaction')">交易</button>
                        <button class="filter-btn" onclick="filterNotifications('security')">安全</button>
                        <button class="filter-btn" onclick="filterNotifications('system')">系统</button>
                    </div>

                    <div class="notification-list">
                        <div class="notification-item unread">
                            <div class="notification-icon icon-success">✓</div>
                            <div class="notification-content">
                                <div class="notification-title">开卡申请成功</div>
                                <div class="notification-message">您的UCard虚拟信用卡申请已成功处理，卡片已激活并可正常使用。</div>
                                <div class="notification-time">2小时前</div>
                                <div class="notification-actions">
                                    <button class="btn-action btn-primary" onclick="loadPage('card-management')">查看卡片</button>
                                    <button class="btn-action" onclick="markAsRead(this)">标记已读</button>
                                </div>
                            </div>
                            <div class="unread-badge"></div>
                        </div>

                        <div class="notification-item unread">
                            <div class="notification-icon icon-info">💰</div>
                            <div class="notification-content">
                                <div class="notification-title">充值到账通知</div>
                                <div class="notification-message">您的购物卡充值$500.00已成功到账，当前余额$1,250.00。</div>
                                <div class="notification-time">3小时前</div>
                                <div class="notification-actions">
                                    <button class="btn-action btn-primary" onclick="loadPage('balance-query')">查看余额</button>
                                    <button class="btn-action" onclick="markAsRead(this)">标记已读</button>
                                </div>
                            </div>
                            <div class="unread-badge"></div>
                        </div>

                        <div class="notification-item">
                            <div class="notification-icon icon-warning">⚠️</div>
                            <div class="notification-content">
                                <div class="notification-title">交易异常提醒</div>
                                <div class="notification-message">检测到您的购物卡在Amazon.com发生$89.99消费，如非本人操作请及时联系客服。</div>
                                <div class="notification-time">1天前</div>
                                <div class="notification-actions">
                                    <button class="btn-action btn-primary" onclick="loadPage('transaction-query')">查看交易</button>
                                    <button class="btn-action">联系客服</button>
                                </div>
                            </div>
                        </div>

                        <div class="notification-item">
                            <div class="notification-icon icon-info">🔒</div>
                            <div class="notification-content">
                                <div class="notification-title">安全验证成功</div>
                                <div class="notification-message">您的手机号验证已完成，账户安全等级已提升。</div>
                                <div class="notification-time">2天前</div>
                            </div>
                        </div>

                        <div class="notification-item">
                            <div class="notification-icon icon-success">📱</div>
                            <div class="notification-content">
                                <div class="notification-title">订阅服务扣费</div>
                                <div class="notification-message">Netflix订阅服务已自动扣费$15.99，服务期限已延长至下月。</div>
                                <div class="notification-time">3天前</div>
                                <div class="notification-actions">
                                    <button class="btn-action btn-primary" onclick="loadPage('transaction-query')">查看详情</button>
                                </div>
                            </div>
                        </div>

                        <div class="notification-item">
                            <div class="notification-icon icon-info">🎉</div>
                            <div class="notification-content">
                                <div class="notification-title">欢迎使用UCard</div>
                                <div class="notification-message">欢迎使用UCard虚拟信用卡服务！您可以随时查看卡片状态、交易记录和账户余额。</div>
                                <div class="notification-time">1周前</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function showRechargeSuccess() {
            const contentBody = document.getElementById('content-body');
            contentBody.innerHTML = `
                <div style="text-align: center; padding: 60px; color: #64748b;">
                    <div style="font-size: 64px; margin-bottom: 20px;">✅</div>
                    <h3 style="color: #1e293b; margin-bottom: 10px;">充值成功</h3>
                    <p>充值已完成，资金已到账</p>
                    <button onclick="loadPage('card-management')" style="margin-top: 20px; padding: 12px 24px; background: #f59e0b; color: white; border: none; border-radius: 8px; cursor: pointer;">返回卡片管理</button>
                </div>
            `;
        }

        // 充值页面相关函数
        let selectedRechargeCard = '';
        let selectedRechargeAccount = '';

        function selectRechargeCard(cardId, cardLabel, cardNumber, cardBalance) {
            selectedRechargeCard = cardId;

            // 更新卡片选择状态
            document.querySelectorAll('.card-option').forEach(card => {
                card.classList.remove('selected');
            });
            event.target.closest('.card-option').classList.add('selected');

            // 显示充值信息部分
            document.getElementById('rechargeSection').classList.add('active');
        }

        function selectRechargeAmount(amount) {
            document.getElementById('rechargeAmount').value = amount;
            document.querySelectorAll('.quick-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            calculateRechargeTotal();
        }

        function selectRechargeAccount(type) {
            selectedRechargeAccount = type;
            document.querySelectorAll('.account-selector').forEach(selector => {
                selector.classList.remove('selected');
            });
            event.target.closest('.account-selector').classList.add('selected');
            calculateRechargeTotal();
        }

        function calculateRechargeTotal() {
            const rechargeAmount = parseFloat(document.getElementById('rechargeAmount').value) || 0;
            const chargeFee = rechargeAmount * 0.02;
            const totalUSD = rechargeAmount + chargeFee;

            document.getElementById('rechargeChargeAmount').textContent = `$${rechargeAmount.toFixed(2)}`;
            document.getElementById('rechargeChargeFee').textContent = `$${chargeFee.toFixed(2)}`;

            if (selectedRechargeAccount) {
                const exchangeRates = getExchangeRates();
                const rate = exchangeRates[selectedRechargeAccount];
                const totalCrypto = totalUSD / rate.toUSD;

                // 显示兑换率
                document.getElementById('rechargeExchangeRateRow').style.display = 'flex';
                document.getElementById('rechargeExchangeRate').textContent = `1 ${selectedRechargeAccount.toUpperCase()} = ${rate.toUSD.toFixed(4)} USD`;
                document.getElementById('rechargeTotalAmount').textContent = `${totalCrypto.toFixed(4)} ${selectedRechargeAccount.toUpperCase()}`;
            } else {
                document.getElementById('rechargeExchangeRateRow').style.display = 'none';
                document.getElementById('rechargeTotalAmount').textContent = '请选择扣款账户';
            }

            checkRechargeFormValidity();
        }

        function checkRechargeFormValidity() {
            const rechargeAmount = parseFloat(document.getElementById('rechargeAmount').value) || 0;
            const cardSelected = selectedRechargeCard !== '';
            const amountValid = rechargeAmount >= 10;
            const accountValid = selectedRechargeAccount !== '';

            const isValid = cardSelected && amountValid && accountValid;
            const submitBtn = document.getElementById('rechargeSubmitBtn');
            if (submitBtn) {
                submitBtn.disabled = !isValid;
            }
        }

        function submitRecharge() {
            const rechargeAmount = document.getElementById('rechargeAmount').value;
            const totalAmount = document.getElementById('rechargeTotalAmount').textContent;

            const rechargeData = {
                amount: rechargeAmount,
                fee: (parseFloat(rechargeAmount) * 0.02).toFixed(2),
                sourceAccount: selectedRechargeAccount === 'usdt' ? 'USDT 账户' : 'USDC 账户',
                totalDeducted: totalAmount,
                cardId: selectedRechargeCard
            };

            localStorage.setItem('rechargeData', JSON.stringify(rechargeData));

            // 安全验证
            showSecurityVerification('card-recharge', () => {
                document.getElementById('rechargeSubmitBtn').textContent = '处理中...';
                document.getElementById('rechargeSubmitBtn').disabled = true;

                setTimeout(() => {
                    loadPage('recharge-success');
                }, 2000);
            });
        }

        // 卡片详情页面
        function showCardDetail() {
            const contentBody = document.getElementById('content-body');
            const cardId = localStorage.getItem('selectedCardId') || '1234';
            const cardData = getCardData(cardId);

            contentBody.innerHTML = `
                <style>
                    .detail-container { max-width: 1000px; margin: 0 auto; }
                    .card-overview { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px; }
                    .card-visual { background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%); border-radius: 16px; padding: 32px; color: white; position: relative; box-shadow: 0 8px 24px 0 rgba(245, 158, 11, 0.3); }
                    .card-visual-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; }
                    .card-visual-label { font-size: 20px; font-weight: 600; }
                    .card-visual-type { font-size: 14px; opacity: 0.9; }
                    .card-visual-number { font-size: 24px; font-weight: 600; letter-spacing: 3px; margin-bottom: 24px; font-family: 'Courier New', monospace; }
                    .card-visual-details { display: flex; justify-content: space-between; align-items: center; }
                    .card-visual-item { text-align: left; }
                    .card-visual-item-label { font-size: 12px; opacity: 0.8; margin-bottom: 4px; }
                    .card-visual-item-value { font-size: 16px; font-weight: 600; }
                    .card-visual-balance { text-align: right; }
                    .card-visual-balance-label { font-size: 12px; opacity: 0.8; margin-bottom: 4px; }
                    .card-visual-balance-amount { font-size: 32px; font-weight: 700; }
                    .card-info { background: white; border: 1px solid #e2e8f0; border-radius: 12px; padding: 24px; }
                    .info-section { margin-bottom: 24px; }
                    .info-section:last-child { margin-bottom: 0; }
                    .info-title { font-size: 16px; font-weight: 600; color: #1e293b; margin-bottom: 12px; }
                    .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 16px; }
                    .info-item { }
                    .info-item-label { font-size: 12px; color: #64748b; margin-bottom: 4px; }
                    .info-item-value { font-size: 14px; color: #1e293b; font-weight: 500; }
                    .status-badge { padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 500; display: inline-block; }
                    .status-normal { background: #d1fae5; color: #065f46; }
                    .status-frozen { background: #fee2e2; color: #991b1b; }
                    .card-actions { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
                    .action-card { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 12px; padding: 20px; text-align: center; cursor: pointer; transition: all 0.2s ease; text-decoration: none; color: inherit; }
                    .action-card:hover { background: #f1f5f9; transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
                    .action-icon { font-size: 32px; margin-bottom: 12px; display: block; }
                    .action-title { font-size: 16px; font-weight: 600; color: #1e293b; margin-bottom: 4px; }
                    .action-desc { font-size: 14px; color: #64748b; }
                    .recent-transactions { background: white; border: 1px solid #e2e8f0; border-radius: 12px; overflow: hidden; }
                    .transaction-header { background: #f8fafc; padding: 16px 24px; border-bottom: 1px solid #e2e8f0; font-weight: 600; color: #1e293b; display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 20px; font-size: 14px; }
                    .transaction-item { padding: 16px 24px; border-bottom: 1px solid #f1f5f9; display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 20px; align-items: center; }
                    .transaction-item:hover { background: #f8fafc; }
                    .transaction-item:last-child { border-bottom: none; }
                    .transaction-type { font-weight: 500; color: #1e293b; }
                    .transaction-amount { font-weight: 600; }
                    .transaction-amount.positive { color: #10b981; }
                    .transaction-amount.negative { color: #ef4444; }
                    .transaction-date { color: #64748b; font-size: 14px; }
                    .transaction-merchant { color: #64748b; font-size: 14px; }
                    .btn-link { background: #f59e0b; color: white; border: none; padding: 12px 24px; border-radius: 8px; font-size: 14px; font-weight: 500; cursor: pointer; text-decoration: none; display: inline-block; transition: all 0.2s ease; }
                    .btn-link:hover { background: #d97706; transform: translateY(-1px); }
                </style>

                <div class="detail-container">
                    <div style="margin-bottom: 20px;">
                        <button onclick="loadPage('card-management')" style="background: none; border: 1px solid #d1d5db; padding: 8px 16px; border-radius: 6px; color: #64748b; cursor: pointer; font-size: 14px;">← 返回卡片管理</button>
                    </div>

                    <div class="card-overview">
                        <div class="card-visual">
                            <div class="card-visual-header">
                                <div class="card-visual-label">${cardData.label}</div>
                                <div class="card-visual-type">Virtual Card</div>
                            </div>
                            <div class="card-visual-number">
                                <span id="detailCardNumber${cardId}">${cardData.number}</span>
                                <button class="eye-icon" onclick="toggleCardSensitiveInfo('${cardId}')" title="查看完整卡号" style="margin-left: 10px;">👁️</button>
                            </div>
                            <div class="card-visual-details">
                                <div class="card-visual-item">
                                    <div class="card-visual-item-label">有效期</div>
                                    <div class="card-visual-item-value" id="detailCardExpiry${cardId}">${cardData.expiry}</div>
                                </div>
                                <div class="card-visual-item">
                                    <div class="card-visual-item-label">CVV</div>
                                    <div class="card-visual-item-value" id="detailCardCvv${cardId}">***</div>
                                </div>
                                <div class="card-visual-balance">
                                    <div class="card-visual-balance-label">当前余额</div>
                                    <div class="card-visual-balance-amount">${cardData.balance}</div>
                                </div>
                            </div>
                        </div>

                        <div class="card-info">
                            <div class="info-section">
                                <div class="info-title">基本信息</div>
                                <div class="info-grid">
                                    <div class="info-item">
                                        <div class="info-item-label">卡片状态</div>
                                        <div class="info-item-value">
                                            <span class="status-badge ${cardData.status === '正常' ? 'status-normal' : 'status-frozen'}">${cardData.status}</span>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-item-label">开卡时间</div>
                                        <div class="info-item-value">${cardData.createDate}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-item-label">卡片类型</div>
                                        <div class="info-item-value">虚拟信用卡</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-item-label">币种</div>
                                        <div class="info-item-value">USD</div>
                                    </div>
                                </div>
                            </div>

                            <div class="info-section">
                                <div class="info-title">限额设置</div>
                                <div class="info-grid">
                                    <div class="info-item">
                                        <div class="info-item-label">单笔限额</div>
                                        <div class="info-item-value">$1,000.00</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-item-label">日限额</div>
                                        <div class="info-item-value">$5,000.00</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-item-label">月限额</div>
                                        <div class="info-item-value">$50,000.00</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-item-label">剩余额度</div>
                                        <div class="info-item-value">$48,750.00</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-actions">
                        <a href="#" class="action-card" onclick="loadPage('card-recharge')">
                            <span class="action-icon">💰</span>
                            <div class="action-title">卡片充值</div>
                            <div class="action-desc">为此卡片充值数字货币</div>
                        </a>

                        <a href="#" class="action-card" onclick="loadPage('transaction-query')">
                            <span class="action-icon">📊</span>
                            <div class="action-title">交易查询</div>
                            <div class="action-desc">查看此卡片的交易记录</div>
                        </a>

                        <a href="#" class="action-card" onclick="loadPage('balance-query')">
                            <span class="action-icon">💵</span>
                            <div class="action-title">余额查询</div>
                            <div class="action-desc">查看此卡片的余额明细</div>
                        </a>

                        <a href="#" class="action-card" onclick="showCardSettings('${cardId}', 'limit')">
                            <span class="action-icon">⚙️</span>
                            <div class="action-title">限额设置</div>
                            <div class="action-desc">修改卡片消费限额</div>
                        </a>
                    </div>

                    <div class="recent-transactions">
                        <div style="padding: 20px 24px; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between; align-items: center;">
                            <h3 style="font-size: 20px; font-weight: 600; color: #1e293b; margin: 0;">最近交易</h3>
                            <a href="#" class="btn-link" onclick="loadPage('transaction-query')">查看全部</a>
                        </div>

                        <div class="transaction-header">
                            <div>交易类型</div>
                            <div>商户/描述</div>
                            <div>金额</div>
                            <div>交易时间</div>
                        </div>

                        <div class="transaction-item">
                            <div class="transaction-type">消费</div>
                            <div class="transaction-merchant">Amazon.com</div>
                            <div class="transaction-amount negative">-$89.99</div>
                            <div class="transaction-date">2024-01-20 14:30</div>
                        </div>

                        <div class="transaction-item">
                            <div class="transaction-type">充值</div>
                            <div class="transaction-merchant">USDT充值</div>
                            <div class="transaction-amount positive">+$500.00</div>
                            <div class="transaction-date">2024-01-19 10:15</div>
                        </div>

                        <div class="transaction-item">
                            <div class="transaction-type">消费</div>
                            <div class="transaction-merchant">Netflix</div>
                            <div class="transaction-amount negative">-$15.99</div>
                            <div class="transaction-date">2024-01-18 09:00</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取卡片数据
        function getCardData(cardId) {
            const cards = {
                '1234': {
                    label: '购物卡',
                    number: '**** **** **** 1234',
                    fullNumber: '4532 1234 5678 1234',
                    cvv: '123',
                    expiry: '12/26',
                    balance: '$1,250.00',
                    status: '正常',
                    createDate: '2024-01-15'
                },
                '5678': {
                    label: '订阅卡',
                    number: '**** **** **** 5678',
                    fullNumber: '4532 9876 5432 5678',
                    cvv: '456',
                    expiry: '08/25',
                    balance: '$500.00',
                    status: '正常',
                    createDate: '2023-08-20'
                }
            };
            return cards[cardId] || cards['1234'];
        }

        // 显示卡片详情
        function showCardDetailById(cardId) {
            localStorage.setItem('selectedCardId', cardId);
            loadPage('card-detail');
        }

        function showCardRechargeById(cardId) {
            localStorage.setItem('selectedCardId', cardId);
            loadPage('card-recharge');
        }

        function clearSelectedCard() {
            localStorage.removeItem('selectedCardId');
        }

        // 其他功能函数
        function filterTransactions() {
            showToast('交易筛选功能开发中', 'info');
        }

        function showTransactionDetail(txId) {
            localStorage.setItem('selectedTransactionId', txId);
            loadPage('transaction-detail');
        }

        function filterNotifications(type) {
            // 更新筛选按钮状态
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 这里可以添加实际的筛选逻辑
            console.log(`筛选通知类型: ${type}`);
        }

        function markAsRead(element) {
            const notificationItem = element.closest('.notification-item');
            notificationItem.classList.remove('unread');
            const unreadBadge = notificationItem.querySelector('.unread-badge');
            if (unreadBadge) {
                unreadBadge.remove();
            }
            element.textContent = '已读';
            element.disabled = true;
        }

        function filterBalanceFlow() {
            showToast('资金流水筛选功能开发中', 'info');
        }

        // 交易详情页面
        function showTransactionDetailPage() {
            const contentBody = document.getElementById('content-body');
            const txId = localStorage.getItem('selectedTransactionId') || 'tx001';
            const txData = getTransactionData(txId);

            contentBody.innerHTML = `
                <style>
                    .detail-container { max-width: 800px; margin: 0 auto; }
                    .detail-header { background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%); border-radius: 16px; padding: 32px; color: white; margin-bottom: 30px; text-align: center; box-shadow: 0 8px 24px 0 rgba(245, 158, 11, 0.3); }
                    .detail-title { font-size: 24px; font-weight: 700; margin-bottom: 8px; }
                    .detail-subtitle { font-size: 16px; opacity: 0.9; }
                    .detail-amount { font-size: 48px; font-weight: 700; margin: 20px 0; }
                    .detail-amount.positive { color: #d1fae5; }
                    .detail-amount.negative { color: #fecaca; }
                    .detail-status { padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 500; display: inline-block; margin-top: 16px; }
                    .status-success { background: rgba(16, 185, 129, 0.2); color: #10b981; }
                    .status-pending { background: rgba(251, 191, 36, 0.2); color: #f59e0b; }
                    .status-failed { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
                    .detail-sections { display: grid; gap: 24px; }
                    .detail-section { background: white; border: 1px solid #e2e8f0; border-radius: 12px; padding: 24px; }
                    .section-title { font-size: 18px; font-weight: 600; color: #1e293b; margin-bottom: 16px; display: flex; align-items: center; }
                    .section-title .icon { margin-right: 10px; font-size: 20px; }
                    .detail-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 16px; }
                    .detail-item { }
                    .detail-label { font-size: 12px; color: #64748b; margin-bottom: 4px; font-weight: 500; }
                    .detail-value { font-size: 14px; color: #1e293b; font-weight: 500; }
                    .detail-full { grid-column: 1 / -1; }
                    .timeline { }
                    .timeline-item { display: flex; align-items: flex-start; margin-bottom: 16px; }
                    .timeline-item:last-child { margin-bottom: 0; }
                    .timeline-dot { width: 12px; height: 12px; border-radius: 50%; margin-right: 16px; margin-top: 4px; flex-shrink: 0; }
                    .timeline-dot.success { background: #10b981; }
                    .timeline-dot.pending { background: #f59e0b; }
                    .timeline-dot.failed { background: #ef4444; }
                    .timeline-content { flex: 1; }
                    .timeline-title { font-size: 14px; font-weight: 500; color: #1e293b; margin-bottom: 2px; }
                    .timeline-time { font-size: 12px; color: #64748b; }
                    .btn-back { background: none; border: 1px solid #d1d5db; padding: 8px 16px; border-radius: 6px; color: #64748b; cursor: pointer; font-size: 14px; margin-bottom: 20px; }
                    .btn-back:hover { background: #f3f4f6; }
                </style>

                <div class="detail-container">
                    <button class="btn-back" onclick="loadPage('transaction-query')">← 返回交易查询</button>

                    <div class="detail-header">
                        <div class="detail-title">${txData.type}</div>
                        <div class="detail-subtitle">${txData.merchant}</div>
                        <div class="detail-amount ${txData.amount.startsWith('-') ? 'negative' : 'positive'}">${txData.amount}</div>
                        <div class="detail-status status-${txData.status === '成功' ? 'success' : txData.status === '处理中' ? 'pending' : 'failed'}">${txData.status}</div>
                    </div>

                    <div class="detail-sections">
                        <div class="detail-section">
                            <div class="section-title">
                                <span class="icon">📋</span>
                                交易信息
                            </div>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <div class="detail-label">交易ID</div>
                                    <div class="detail-value">${txData.id}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">交易类型</div>
                                    <div class="detail-value">${txData.type}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">交易金额</div>
                                    <div class="detail-value">${txData.amount}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">交易状态</div>
                                    <div class="detail-value">${txData.status}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">商户名称</div>
                                    <div class="detail-value">${txData.merchant}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">交易时间</div>
                                    <div class="detail-value">${txData.time}</div>
                                </div>
                                <div class="detail-item detail-full">
                                    <div class="detail-label">交易描述</div>
                                    <div class="detail-value">${txData.description}</div>
                                </div>
                            </div>
                        </div>

                        <div class="detail-section">
                            <div class="section-title">
                                <span class="icon">💳</span>
                                卡片信息
                            </div>
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <div class="detail-label">卡片名称</div>
                                    <div class="detail-value">${txData.cardName}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">卡号</div>
                                    <div class="detail-value">${txData.cardNumber}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">交易前余额</div>
                                    <div class="detail-value">${txData.balanceBefore}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">交易后余额</div>
                                    <div class="detail-value">${txData.balanceAfter}</div>
                                </div>
                            </div>
                        </div>

                        <div class="detail-section">
                            <div class="section-title">
                                <span class="icon">🔄</span>
                                处理进度
                            </div>
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-dot success"></div>
                                    <div class="timeline-content">
                                        <div class="timeline-title">交易发起</div>
                                        <div class="timeline-time">${txData.time}</div>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-dot success"></div>
                                    <div class="timeline-content">
                                        <div class="timeline-title">商户授权</div>
                                        <div class="timeline-time">${txData.authTime}</div>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-dot ${txData.status === '成功' ? 'success' : txData.status === '处理中' ? 'pending' : 'failed'}"></div>
                                    <div class="timeline-content">
                                        <div class="timeline-title">${txData.status === '成功' ? '交易完成' : txData.status === '处理中' ? '处理中' : '交易失败'}</div>
                                        <div class="timeline-time">${txData.completeTime}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 获取交易数据
        function getTransactionData(txId) {
            const transactions = {
                'tx001': {
                    id: 'TX20240120143001',
                    type: '消费',
                    amount: '-$89.99',
                    status: '成功',
                    merchant: 'Amazon.com',
                    time: '2024-01-20 14:30:15',
                    description: 'Amazon.com 在线购物消费',
                    cardName: '购物卡',
                    cardNumber: '**** **** **** 1234',
                    balanceBefore: '$1,339.99',
                    balanceAfter: '$1,250.00',
                    authTime: '2024-01-20 14:30:18',
                    completeTime: '2024-01-20 14:30:22'
                },

                'tx003': {
                    id: 'TX20240118090001',
                    type: '消费',
                    amount: '-$15.99',
                    status: '成功',
                    merchant: 'Netflix',
                    time: '2024-01-18 09:00:12',
                    description: 'Netflix 月度订阅服务',
                    cardName: '订阅卡',
                    cardNumber: '**** **** **** 5678',
                    balanceBefore: '$515.99',
                    balanceAfter: '$500.00',
                    authTime: '2024-01-18 09:00:15',
                    completeTime: '2024-01-18 09:00:18'
                },
                'tx004': {
                    id: 'TX20240117204501',
                    type: '消费',
                    amount: '-$59.99',
                    status: '处理中',
                    merchant: 'Steam Store',
                    time: '2024-01-17 20:45:30',
                    description: 'Steam 游戏平台购买',
                    cardName: '购物卡',
                    cardNumber: '**** **** **** 1234',
                    balanceBefore: '$809.99',
                    balanceAfter: '$750.00',
                    authTime: '2024-01-17 20:45:35',
                    completeTime: '处理中...'
                },
                'tx005': {
                    id: 'TX20240116162001',
                    type: '退款',
                    amount: '+$29.99',
                    status: '成功',
                    merchant: 'Apple Store',
                    time: '2024-01-16 16:20:45',
                    description: 'Apple Store 应用退款',
                    cardName: '购物卡',
                    cardNumber: '**** **** **** 1234',
                    balanceBefore: '$779.99',
                    balanceAfter: '$809.99',
                    authTime: '2024-01-16 16:20:50',
                    completeTime: '2024-01-16 16:21:05'
                }
            };
            return transactions[txId] || transactions['tx001'];
        }

        // Toast 提示功能
        function showToast(message, type = 'info') {
            // 创建toast容器（如果不存在）
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                `;
                document.body.appendChild(toastContainer);
            }

            // 创建toast元素
            const toast = document.createElement('div');
            const colors = {
                success: { bg: '#d1fae5', border: '#10b981', text: '#065f46' },
                warning: { bg: '#fef3c7', border: '#f59e0b', text: '#92400e' },
                error: { bg: '#fee2e2', border: '#ef4444', text: '#991b1b' },
                info: { bg: '#dbeafe', border: '#3b82f6', text: '#1e40af' }
            };
            const color = colors[type] || colors.info;

            toast.style.cssText = `
                background: ${color.bg};
                border: 1px solid ${color.border};
                color: ${color.text};
                padding: 12px 16px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                max-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            toast.textContent = message;

            toastContainer.appendChild(toast);

            // 动画显示
            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 10);

            // 自动隐藏
            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 卡片设置功能
        function showCardSettings(cardId, action) {
            const cardData = getCardData(cardId);

            switch (action) {
                case 'limit':
                    localStorage.setItem('selectedCardId', cardId);
                    loadPage('card-limit-settings');
                    return;
                case 'freeze':
                    // 冻结操作需要安全验证
                    showSecurityVerification('card-freeze', () => {
                        showToast(`${cardData.label} 已冻结`, 'warning');
                    });
                    break;
                case 'unfreeze':
                    // 解冻操作需要安全验证
                    showSecurityVerification('card-unfreeze', () => {
                        showToast(`${cardData.label} 已解冻`, 'success');
                    });
                    break;
                case 'cancel':
                    showToast(`${cardData.label} 注销功能开发中`, 'warning');
                    break;
                default:
                    showToast('功能开发中', 'info');
            }
        }

        // 限额设置页面
        function showCardLimitSettings() {
            const contentBody = document.getElementById('content-body');
            const cardId = localStorage.getItem('selectedCardId') || '1234';
            const cardData = getCardData(cardId);
            const currentLimits = getCurrentLimits(cardId);

            contentBody.innerHTML = `
                <style>
                    .limit-container { max-width: 800px; margin: 0 auto; }
                    .card-header { background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%); border-radius: 16px; padding: 24px; color: white; margin-bottom: 30px; text-align: center; box-shadow: 0 8px 24px 0 rgba(245, 158, 11, 0.3); }
                    .card-title { font-size: 24px; font-weight: 700; margin-bottom: 8px; }
                    .card-subtitle { font-size: 16px; opacity: 0.9; }
                    .limit-form { background: white; border: 1px solid #e2e8f0; border-radius: 12px; padding: 32px; }
                    .form-section { margin-bottom: 32px; }
                    .form-section:last-child { margin-bottom: 0; }
                    .section-title { font-size: 20px; font-weight: 600; color: #1e293b; margin-bottom: 20px; display: flex; align-items: center; }
                    .section-title .icon { margin-right: 10px; font-size: 24px; }
                    .limit-item { background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin-bottom: 16px; }
                    .limit-item:last-child { margin-bottom: 0; }
                    .limit-header { display: flex; justify-content: between; align-items: center; margin-bottom: 16px; }
                    .limit-label { font-size: 16px; font-weight: 600; color: #1e293b; flex: 1; }
                    .limit-current { font-size: 14px; color: #64748b; }
                    .limit-input-group { display: flex; align-items: center; gap: 12px; }
                    .limit-input { flex: 1; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 16px; font-weight: 500; }
                    .limit-input:focus { outline: none; border-color: #f59e0b; box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2); }
                    .limit-unit { font-size: 16px; font-weight: 500; color: #64748b; }
                    .limit-description { font-size: 14px; color: #64748b; margin-top: 8px; line-height: 1.4; }
                    .current-usage { background: white; border: 1px solid #e2e8f0; border-radius: 12px; padding: 24px; margin-bottom: 30px; }
                    .usage-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
                    .usage-item { text-align: center; }
                    .usage-label { font-size: 14px; color: #64748b; margin-bottom: 8px; }
                    .usage-value { font-size: 24px; font-weight: 700; color: #f59e0b; margin-bottom: 4px; }
                    .usage-limit { font-size: 12px; color: #9ca3af; }
                    .usage-bar { width: 100%; height: 6px; background: #e2e8f0; border-radius: 3px; margin-top: 8px; overflow: hidden; }
                    .usage-progress { height: 100%; background: linear-gradient(90deg, #10b981, #f59e0b, #ef4444); border-radius: 3px; transition: width 0.3s ease; }
                    .btn-group { display: flex; gap: 16px; justify-content: flex-end; margin-top: 32px; }
                    .btn { padding: 12px 24px; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.2s ease; }
                    .btn-secondary { background: #6b7280; color: white; }
                    .btn-primary { background: #f59e0b; color: white; }
                    .btn:hover { transform: translateY(-1px); }
                    .btn-back { background: none; border: 1px solid #d1d5db; padding: 8px 16px; border-radius: 6px; color: #64748b; cursor: pointer; font-size: 14px; margin-bottom: 20px; }
                    .btn-back:hover { background: #f3f4f6; }
                    .warning-box { background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 16px; margin-bottom: 24px; }
                    .warning-title { font-size: 14px; font-weight: 600; color: #92400e; margin-bottom: 4px; }
                    .warning-text { font-size: 14px; color: #92400e; line-height: 1.4; }
                </style>

                <div class="limit-container">
                    <button class="btn-back" onclick="loadPage('card-detail')">← 返回卡片详情</button>

                    <div class="card-header">
                        <div class="card-title">${cardData.label} 限额设置</div>
                        <div class="card-subtitle">${cardData.number}</div>
                    </div>

                    <div class="current-usage">
                        <h3 style="font-size: 18px; font-weight: 600; color: #1e293b; margin-bottom: 20px; text-align: center;">当前使用情况</h3>
                        <div class="usage-grid">
                            <div class="usage-item">
                                <div class="usage-label">今日已消费</div>
                                <div class="usage-value">$${currentLimits.dailyUsed}</div>
                                <div class="usage-limit">/ $${currentLimits.dailyLimit}</div>
                                <div class="usage-bar">
                                    <div class="usage-progress" style="width: ${(currentLimits.dailyUsed / currentLimits.dailyLimit * 100).toFixed(1)}%"></div>
                                </div>
                            </div>
                            <div class="usage-item">
                                <div class="usage-label">本月已消费</div>
                                <div class="usage-value">$${currentLimits.monthlyUsed}</div>
                                <div class="usage-limit">/ $${currentLimits.monthlyLimit}</div>
                                <div class="usage-bar">
                                    <div class="usage-progress" style="width: ${(currentLimits.monthlyUsed / currentLimits.monthlyLimit * 100).toFixed(1)}%"></div>
                                </div>
                            </div>
                            <div class="usage-item">
                                <div class="usage-label">剩余额度</div>
                                <div class="usage-value">$${(currentLimits.monthlyLimit - currentLimits.monthlyUsed).toFixed(2)}</div>
                                <div class="usage-limit">本月剩余</div>
                            </div>
                        </div>
                    </div>

                    <div class="warning-box">
                        <div class="warning-title">⚠️ 重要提醒</div>
                        <div class="warning-text">修改限额设置后将立即生效。降低限额可能会影响正在处理的交易，请谨慎操作。</div>
                    </div>

                    <form class="limit-form" id="limitForm">
                        <div class="form-section">
                            <div class="section-title">
                                <span class="icon">💳</span>
                                消费限额设置
                            </div>

                            <div class="limit-item">
                                <div class="limit-header">
                                    <div class="limit-label">单笔消费限额</div>
                                    <div class="limit-current">当前: $${currentLimits.singleLimit}</div>
                                </div>
                                <div class="limit-input-group">
                                    <span class="limit-unit">$</span>
                                    <input type="number" class="limit-input" id="singleLimit" value="${currentLimits.singleLimit}" min="1" max="10000" step="0.01">
                                    <span class="limit-unit">USD</span>
                                </div>
                                <div class="limit-description">单次交易的最大金额限制，建议设置为您日常消费的最大金额</div>
                            </div>

                            <div class="limit-item">
                                <div class="limit-header">
                                    <div class="limit-label">单日消费限额</div>
                                    <div class="limit-current">当前: $${currentLimits.dailyLimit}</div>
                                </div>
                                <div class="limit-input-group">
                                    <span class="limit-unit">$</span>
                                    <input type="number" class="limit-input" id="dailyLimit" value="${currentLimits.dailyLimit}" min="1" max="50000" step="0.01">
                                    <span class="limit-unit">USD</span>
                                </div>
                                <div class="limit-description">每日累计消费的最大金额限制，超过此限额当日将无法继续消费</div>
                            </div>

                            <div class="limit-item">
                                <div class="limit-header">
                                    <div class="limit-label">每月消费限额</div>
                                    <div class="limit-current">当前: $${currentLimits.monthlyLimit}</div>
                                </div>
                                <div class="limit-input-group">
                                    <span class="limit-unit">$</span>
                                    <input type="number" class="limit-input" id="monthlyLimit" value="${currentLimits.monthlyLimit}" min="1" max="100000" step="0.01">
                                    <span class="limit-unit">USD</span>
                                </div>
                                <div class="limit-description">每月累计消费的最大金额限制，有助于控制月度支出</div>
                            </div>
                        </div>

                        <div class="btn-group">
                            <button type="button" class="btn btn-secondary" onclick="resetLimits()">重置为默认</button>
                            <button type="button" class="btn btn-primary" onclick="saveLimitSettings()">保存设置</button>
                        </div>
                    </form>
                </div>
            `;
        }

        // 获取当前限额设置
        function getCurrentLimits(cardId) {
            const limits = {
                '1234': {
                    singleLimit: 1000.00,
                    dailyLimit: 5000.00,
                    monthlyLimit: 50000.00,
                    dailyUsed: 105.98,
                    monthlyUsed: 1250.00
                },
                '5678': {
                    singleLimit: 500.00,
                    dailyLimit: 2000.00,
                    monthlyLimit: 20000.00,
                    dailyUsed: 15.99,
                    monthlyUsed: 515.99
                }
            };
            return limits[cardId] || limits['1234'];
        }

        // 重置限额为默认值
        function resetLimits() {
            document.getElementById('singleLimit').value = '1000.00';
            document.getElementById('dailyLimit').value = '5000.00';
            document.getElementById('monthlyLimit').value = '50000.00';
            showToast('已重置为默认限额', 'info');
        }

        // 保存限额设置
        function saveLimitSettings() {
            const singleLimit = parseFloat(document.getElementById('singleLimit').value);
            const dailyLimit = parseFloat(document.getElementById('dailyLimit').value);
            const monthlyLimit = parseFloat(document.getElementById('monthlyLimit').value);

            // 验证限额设置
            if (singleLimit <= 0 || dailyLimit <= 0 || monthlyLimit <= 0) {
                showToast('限额必须大于0', 'error');
                return;
            }

            if (singleLimit > dailyLimit) {
                showToast('单笔限额不能超过单日限额', 'error');
                return;
            }

            if (dailyLimit > monthlyLimit) {
                showToast('单日限额不能超过每月限额', 'error');
                return;
            }

            // 安全验证
            showSecurityVerification('limit-settings', () => {
                const cardId = localStorage.getItem('selectedCardId') || '1234';
                const cardData = getCardData(cardId);

                showToast(`${cardData.label} 限额设置已保存`, 'success');

                // 延迟返回卡片详情页面
                setTimeout(() => {
                    loadPage('card-detail');
                }, 1500);
            });
        }

        // 获取实时兑换率
        function getExchangeRates() {
            // 模拟实时兑换率，实际应用中应该从API获取
            return {
                usdt: {
                    toUSD: 1.0002,
                    symbol: 'USDT',
                    name: 'Tether USD'
                },
                usdc: {
                    toUSD: 0.9998,
                    symbol: 'USDC',
                    name: 'USD Coin'
                }
            };
        }

        // 安全验证系统
        // 全局变量存储验证回调
        let currentVerificationCallback = null;

        function showSecurityVerification(action, callback) {
            currentVerificationCallback = callback;

            const modal = document.createElement('div');
            modal.id = 'securityModal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;

            modal.innerHTML = `
                <div style="
                    background: white;
                    border-radius: 16px;
                    padding: 32px;
                    max-width: 400px;
                    width: 90%;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                ">
                    <div style="text-align: center; margin-bottom: 24px;">
                        <div style="font-size: 48px; margin-bottom: 16px;">🔐</div>
                        <h3 style="font-size: 20px; font-weight: 600; color: #1e293b; margin-bottom: 8px;">安全验证</h3>
                        <p style="font-size: 14px; color: #64748b;">为了保护您的账户安全，请完成身份验证</p>
                    </div>

                    <div style="margin-bottom: 24px;">
                        <div style="display: flex; gap: 12px; margin-bottom: 20px;">
                            <button id="totpVerifyBtn" class="verify-method-btn active" onclick="switchVerifyMethod('totp')" style="
                                flex: 1;
                                padding: 12px;
                                border: 2px solid #f59e0b;
                                background: #f59e0b;
                                color: white;
                                border-radius: 8px;
                                font-size: 14px;
                                font-weight: 500;
                                cursor: pointer;
                                transition: all 0.2s ease;
                            ">🔑 谷歌验证器</button>
                            <button id="smsVerifyBtn" class="verify-method-btn" onclick="switchVerifyMethod('sms')" style="
                                flex: 1;
                                padding: 12px;
                                border: 2px solid #e2e8f0;
                                background: white;
                                color: #64748b;
                                border-radius: 8px;
                                font-size: 14px;
                                font-weight: 500;
                                cursor: pointer;
                                transition: all 0.2s ease;
                            ">📱 短信验证</button>
                        </div>

                        <div id="totpVerification" style="display: block;">
                            <div style="margin-bottom: 16px;">
                                <label style="display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 8px;">
                                    请输入谷歌验证器中的6位数字
                                </label>
                                <input type="text" id="totpCode" placeholder="请输入6位验证码" maxlength="6" style="
                                    width: 100%;
                                    padding: 12px;
                                    border: 1px solid #d1d5db;
                                    border-radius: 8px;
                                    font-size: 16px;
                                    text-align: center;
                                    letter-spacing: 4px;
                                " oninput="validateVerificationCode()">
                            </div>
                            <div style="font-size: 12px; color: #64748b; text-align: center;">
                                验证码每30秒更新一次
                            </div>
                        </div>

                        <div id="smsVerification" style="display: none;">
                            <div style="margin-bottom: 16px;">
                                <label style="display: block; font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 8px;">
                                    验证码已发送至 ******-***-890
                                </label>
                                <input type="text" id="smsCode" placeholder="请输入6位验证码" maxlength="6" style="
                                    width: 100%;
                                    padding: 12px;
                                    border: 1px solid #d1d5db;
                                    border-radius: 8px;
                                    font-size: 16px;
                                    text-align: center;
                                    letter-spacing: 4px;
                                " oninput="validateVerificationCode()">
                            </div>
                            <button onclick="resendSmsCode()" style="
                                background: none;
                                border: none;
                                color: #f59e0b;
                                font-size: 14px;
                                cursor: pointer;
                                text-decoration: underline;
                            ">重新发送验证码</button>
                        </div>
                    </div>

                    <div style="display: flex; gap: 12px;">
                        <button onclick="closeSecurityModal()" style="
                            flex: 1;
                            padding: 12px;
                            border: 1px solid #d1d5db;
                            background: white;
                            color: #64748b;
                            border-radius: 8px;
                            font-size: 16px;
                            font-weight: 500;
                            cursor: pointer;
                        ">取消</button>
                        <button id="verifySubmitBtn" onclick="submitVerification()" disabled style="
                            flex: 1;
                            padding: 12px;
                            border: none;
                            background: #d1d5db;
                            color: white;
                            border-radius: 8px;
                            font-size: 16px;
                            font-weight: 500;
                            cursor: not-allowed;
                        ">验证</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        function switchVerifyMethod(method) {
            const smsBtn = document.getElementById('smsVerifyBtn');
            const totpBtn = document.getElementById('totpVerifyBtn');
            const smsDiv = document.getElementById('smsVerification');
            const totpDiv = document.getElementById('totpVerification');

            if (method === 'sms') {
                smsBtn.style.background = '#f59e0b';
                smsBtn.style.color = 'white';
                smsBtn.style.borderColor = '#f59e0b';
                totpBtn.style.background = 'white';
                totpBtn.style.color = '#64748b';
                totpBtn.style.borderColor = '#e2e8f0';
                smsDiv.style.display = 'block';
                totpDiv.style.display = 'none';
                document.getElementById('smsCode').value = '';
                // 切换到短信验证时发送验证码
                showToast('验证码已发送至您的手机', 'info');
            } else {
                totpBtn.style.background = '#f59e0b';
                totpBtn.style.color = 'white';
                totpBtn.style.borderColor = '#f59e0b';
                smsBtn.style.background = 'white';
                smsBtn.style.color = '#64748b';
                smsBtn.style.borderColor = '#e2e8f0';
                totpDiv.style.display = 'block';
                smsDiv.style.display = 'none';
                document.getElementById('totpCode').value = '';
            }
            validateVerificationCode();
        }

        function validateVerificationCode() {
            const smsCode = document.getElementById('smsCode').value;
            const totpCode = document.getElementById('totpCode').value;
            const submitBtn = document.getElementById('verifySubmitBtn');
            const totpVisible = document.getElementById('totpVerification').style.display === 'block';

            const code = totpVisible ? totpCode : smsCode;
            const isValid = code.length === 6 && /^\d{6}$/.test(code);

            if (isValid) {
                submitBtn.disabled = false;
                submitBtn.style.background = '#f59e0b';
                submitBtn.style.cursor = 'pointer';
            } else {
                submitBtn.disabled = true;
                submitBtn.style.background = '#d1d5db';
                submitBtn.style.cursor = 'not-allowed';
            }
        }

        function resendSmsCode() {
            showToast('验证码已重新发送', 'info');
        }

        function submitVerification() {
            const smsCode = document.getElementById('smsCode').value;
            const totpCode = document.getElementById('totpCode').value;
            const totpVisible = document.getElementById('totpVerification').style.display === 'block';

            const code = totpVisible ? totpCode : smsCode;

            // 模拟验证过程
            const submitBtn = document.getElementById('verifySubmitBtn');
            submitBtn.textContent = '验证中...';
            submitBtn.disabled = true;

            setTimeout(() => {
                // 模拟验证成功（实际应用中应该调用后端API）
                if (code === '123456' || Math.random() > 0.3) {
                    closeSecurityModal();
                    showToast('验证成功', 'success');
                    if (currentVerificationCallback) {
                        setTimeout(currentVerificationCallback, 500);
                        currentVerificationCallback = null;
                    }
                } else {
                    submitBtn.textContent = '验证';
                    submitBtn.disabled = false;
                    showToast('验证码错误，请重试', 'error');
                }
            }, 1500);
        }

        function closeSecurityModal() {
            const modal = document.getElementById('securityModal');
            if (modal) {
                modal.remove();
            }
        }

        // 卡片敏感信息显示切换
        function toggleCardSensitiveInfo(cardId) {
            showSecurityVerification('view-card-info', () => {
                showCardSensitiveInfo(cardId);
            });
        }

        function showCardSensitiveInfo(cardId) {
            const cardData = getCardData(cardId);

            // 更新所有可能的卡号元素
            const cardNumberElements = [
                document.getElementById(`cardNumber${cardId}`),
                document.getElementById(`jsCardNumber${cardId}`),
                document.getElementById(`detailCardNumber${cardId}`)
            ];

            const cvvElements = [
                document.getElementById(`cardCvv${cardId}`),
                document.getElementById(`jsCardCvv${cardId}`),
                document.getElementById(`detailCardCvv${cardId}`)
            ];

            const expiryElements = [
                document.getElementById(`cardExpiry${cardId}`),
                document.getElementById(`jsCardExpiry${cardId}`),
                document.getElementById(`detailCardExpiry${cardId}`)
            ];

            cardNumberElements.forEach(element => {
                if (element) {
                    element.textContent = cardData.fullNumber;
                }
            });

            cvvElements.forEach(element => {
                if (element) {
                    element.textContent = cardData.cvv;
                }
            });

            expiryElements.forEach(element => {
                if (element) {
                    element.textContent = cardData.expiry;
                }
            });

            // 显示成功提示
            showToast('敏感信息已显示，5秒后自动隐藏', 'success');

            // 5秒后自动隐藏敏感信息
            setTimeout(() => {
                hideCardSensitiveInfo(cardId);
            }, 5000);
        }

        function hideCardSensitiveInfo(cardId) {
            const cardData = getCardData(cardId);

            const cardNumberElements = [
                document.getElementById(`cardNumber${cardId}`),
                document.getElementById(`jsCardNumber${cardId}`),
                document.getElementById(`detailCardNumber${cardId}`)
            ];

            const cvvElements = [
                document.getElementById(`cardCvv${cardId}`),
                document.getElementById(`jsCardCvv${cardId}`),
                document.getElementById(`detailCardCvv${cardId}`)
            ];

            const expiryElements = [
                document.getElementById(`cardExpiry${cardId}`),
                document.getElementById(`jsCardExpiry${cardId}`),
                document.getElementById(`detailCardExpiry${cardId}`)
            ];

            cardNumberElements.forEach(element => {
                if (element) {
                    element.textContent = `**** **** **** ${cardId}`;
                }
            });

            cvvElements.forEach(element => {
                if (element) {
                    element.textContent = '***';
                }
            });

            expiryElements.forEach(element => {
                if (element) {
                    element.textContent = cardData.expiry;
                }
            });

            // 显示隐藏提示
            showToast('敏感信息已隐藏', 'info');
        }

        console.log('Script loaded successfully');
    </script>
</body>
</html>
